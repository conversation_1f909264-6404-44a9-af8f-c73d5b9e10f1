import { Linking, Share } from 'react-native';

// Base URLs for deep linking
const DEEP_LINK_SCHEME = 'voitto://';
const WEB_BASE_URL = 'https://voitto.app';

export type DeepLinkParams = Record<string, string | number | boolean | undefined | null>;

export interface ParsedDeepLink {
  screen: string | null;
  params: Record<string, string>;
  originalUrl: string;
  path: string;
}

/**
 * Generate a deep link URL for a specific screen and parameters
 */
export const generateDeepLink = (
  screen: string,
  params: DeepLinkParams = {},
  useWebUrl: boolean = false
): string => {
  const baseUrl = useWebUrl ? WEB_BASE_URL : DEEP_LINK_SCHEME;

  let path = '';
  const queryParams = new URLSearchParams();

  // Add non-path parameters to query string
  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  const queryString = queryParams.toString();

  switch (screen) {
    case 'ProductDetails':
      path = `/product/${params.id || ''}`;
      break;
    default:
      path = `/${screen.toLowerCase()}`;
  }

  return `${baseUrl}${path}${queryString ? `?${queryString}` : ''}`;
};

/**
 * Parse a deep link URL and extract screen and parameters
 */
export const parseDeepLink = (url: string): ParsedDeepLink | null => {
  if (!url) return null;

  try {
    const urlObj = new URL(url);
    const path = urlObj.pathname;
    const params: Record<string, string> = Object.fromEntries(urlObj.searchParams);

    let screen: string | null = null;
    let routeParams = { ...params };

    if (path.includes('/product/')) {
      screen = 'ProductDetails';
      const productId = path.split('/product/')[1];
      if (productId) routeParams.id = productId;
    }

    return {
      screen,
      params: routeParams,
      originalUrl: url,
      path
    };
  } catch (error) {
    console.log('Error parsing deep link:', error);
    return null;
  }
};

/**
 * Open a deep link URL
 */
export const openDeepLink = async (url: string): Promise<boolean> => {
  try {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
      return true;
    } else {
      console.log('Cannot open URL:', url);
      return false;
    }
  } catch (error) {
    console.log('Error opening deep link:', error);
    return false;
  }
};

/**
 * Share a deep link URL
 */
export const shareDeepLink = async (
  screen: string,
  params: DeepLinkParams = {},
  title: string = 'Check this out!',
  message: string = ''
): Promise<boolean> => {
  try {
    const webUrl = generateDeepLink(screen, params, true);
    const result = await Share.share({
      title,
      message: webUrl,
      url: webUrl
    });

    return result.action === Share.sharedAction;
  } catch (error) {
    console.log('Error sharing deep link:', error);
    return false;
  }
};

/**
 * Validate if a URL is a valid deep link for this app
 */
export const isValidDeepLink = (url: string): boolean => {
  if (!url) return false;

  try {
    const urlObj = new URL(url);
    const isCustomScheme = url.startsWith(DEEP_LINK_SCHEME);
    const isWebUrl = urlObj.hostname === 'voitto.app';

    return isCustomScheme || isWebUrl;
  } catch (error) {
    return false;
  }
};

/**
 * Get the current deep link URL for a screen
 */
export const getDeepLinkUrls = (
  screen: string,
  params: DeepLinkParams = {}
): { customScheme: string; webUrl: string } => {
  return {
    customScheme: generateDeepLink(screen, params, false),
    webUrl: generateDeepLink(screen, params, true)
  };
};
