import {StyleSheet, Text, View, TextInputProps} from 'react-native';
import React, {ElementType, useState} from 'react';
import {colors, fonts} from '../../utilities/theme';
import ToggleSwitch from 'toggle-switch-react-native';
interface Props extends TextInputProps {}
const ToggleBtn: React.FC<Props> = () => {
  const [isToggled, setIsToggled] = useState(false);
  const toggleButton = () => {
    setIsToggled(!isToggled);
  };
  return (
    <View style={styles.toggleContainer}>
      <ToggleSwitch
        isOn={isToggled}
        onColor="#FFFFFF"
        thumbOnStyle={{
          backgroundColor: colors.primary,
        }}
        trackOnStyle={{
          borderColor: colors.primary,
          borderWidth: 2,

          backgroundColor: 'white',
          paddingHorizontal: 22,
        }}
        thumbOffStyle={{
          backgroundColor: '#ccc',
        }}
        trackOffStyle={{
          backgroundColor: 'white',
          borderWidth: 2,
          borderColor: '#ccc',
          paddingHorizontal: 22,
        }}
        size="medium"
        onToggle={toggleButton}
      />
      <Text style={styles.toggleText}>Mark as default card</Text>
    </View>
  );
};

export default ToggleBtn;
const styles = StyleSheet.create({
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },

  toggleText: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    marginLeft: 10,
  },
});
