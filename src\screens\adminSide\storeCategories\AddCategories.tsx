import { StyleSheet, View, Text, TextInput } from "react-native";
import React, { useEffect, useRef, useState } from "react";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AppButton } from "../../../component";
import { appStyles, colors } from "../../../utilities/theme";
import AdminInput from "../../../component/adminCommon/AdminInput";
import { useFormik } from "formik";
import * as Yup from "yup";
import firestore from "@react-native-firebase/firestore";
import { showToast } from "../../../helper/toast";
import { ICategories } from "../../../interfaces/Products";
type Props = NativeStackScreenProps<AdminStackParamsList, "AddCategories">;
const AddCategories: React.FC<Props> = ({ route, navigation }) => {
  const { category } = route.params || {};
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const updateCategories = async (values: ICategories) => {
    try {
      setIsLoading(true);
      const currentTime = firestore.Timestamp.now();
      const existingCategoryQuery = await firestore()
        .collection("Categories")
        .where("categoryLabel", "==", values.categoryLabel)
        .get();
      if (!existingCategoryQuery.empty) {
        showToast("This category is already created!");
        setIsLoading(false);
        return;
      }

      const categoryDocRef = firestore().collection("Categories").doc(values.id);
      const categoryDoc = await categoryDocRef.get();

      if (categoryDoc.exists) {
        // If the category exists, update it with `updatedAt` and `updatedAt`
        await categoryDocRef.update({
          ...values,
          updatedAt: currentTime,
        });
      } else {
        // If the category doesn't exist, create it with `createdAt`, `updatedAt` as null, and `updatedAt`
        const userDocRef = firestore().collection("Categories").doc();
        await userDocRef.set({
          ...values,
          id: userDocRef.id,
          createdAt: currentTime,
          updatedAt: currentTime,
        });
      }
      setIsLoading(false);
      navigation.goBack();
    } catch (error) {
      console.error("Error adding or updating category details:", error);
      setIsLoading(false);
      showToast("Could not add or update category details!");
    }
  };

  const validationSchema = Yup.object().shape({
    categoryLabel: Yup.string().required("Category is required"),
  });

  const formik = useFormik({
    initialValues: {
      id: category?.id || "",
      categoryLabel: category?.categoryLabel || "",
      createdAt: category?.createdAt || null,
      updatedAt: category?.updatedAt || null,
    },
    validationSchema: validationSchema,
    enableReinitialize: true,
    onSubmit: updateCategories,
  });
  useEffect(() => {
    navigation.setOptions({
      headerTitle: category?.id ? "Update Category" : "Add Category",
    });
  }, []);
  return (
    <View style={[styles.container]}>
      <View style={{ paddingHorizontal: 28 }}>
        <Text style={[appStyles.body5, { marginTop: 10 }]}>Category</Text>
        <AdminInput
          ref={inputRef}
          placeholder="Category"
          inputContainerStyle={{ marginTop: 5, marginBottom: 12 }}
          onChangeText={formik.handleChange("categoryLabel")}
          value={formik.values.categoryLabel}
          onBlur={formik.handleBlur("categoryLabel")}
          errorMessage={formik.touched.categoryLabel && formik.errors.categoryLabel}
        />
        <AppButton title="Save" onPress={formik.handleSubmit} isLoading={isLoading} />
      </View>
    </View>
  );
};

export default AddCategories;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainerStyle: {
    flexGrow: 1,
    marginHorizontal: 4,
    paddingHorizontal: 28,
    paddingBottom: 20,
  },
});
