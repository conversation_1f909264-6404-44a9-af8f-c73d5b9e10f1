import { StyleSheet, View, Text, FlatList, SafeAreaView, Dimensions, ActivityIndicator, Pressable } from "react-native";
import React, { useEffect, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import { colors, fonts } from "../../../utilities/theme";
import { NotificationCheckIcon } from "../../../assets/svg";
import firestore, { FirebaseFirestoreTypes } from "@react-native-firebase/firestore";
import { useUser } from "../../../Hooks/UseContext";
import { formatDistanceToNow } from "date-fns";
import useNotification from "../../../Hooks/useNotification";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";

type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList & AdminBottomTabParamlist, "Notification">;

type NotificationType = {
  id: string;
  title: string;
  body: string;
  status: string;
  createdAt: FirebaseFirestoreTypes.Timestamp;
  recipientId?: string;
};

const Notification: React.FC<Props> = ({ navigation }) => {
  const { user } = useUser();
  const { markNotificationsAsRead } = useNotification();

  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastVisible, setLastVisible] = useState<FirebaseFirestoreTypes.DocumentSnapshot | null>(null);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [hasScrolled, setHasScrolled] = useState(false);
  const [allDataFetched, setAllDataFetched] = useState(false);

  const pageSize = 20;

  useEffect(() => {
    user?.userId && markNotificationsAsRead(user?.userId);
  }, []);

  useEffect(() => {
    if (!user?.userId) return;

    const fetchInitialNotifications = async () => {
      try {
        const snapshot = await firestore()
          .collection("Notifications")
          .where("userId", "==", user?.userId)
          .orderBy("createdAt", "desc")
          .limit(pageSize)
          .get();

        const notifList: NotificationType[] = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as NotificationType[];

        setNotifications(notifList);

        if (!snapshot.empty) {
          setLastVisible(snapshot.docs[snapshot.docs.length - 1]);
        }

        if (snapshot.empty || snapshot.size < pageSize) {
          setAllDataFetched(true); // No more notifications
        }
      } catch (err) {
        console.error("Initial notifications fetch error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialNotifications();
  }, [user?.userId]);

  const fetchMoreNotifications = async () => {
    if (!lastVisible || isFetchingMore || allDataFetched) return;

    setIsFetchingMore(true);
    try {
      const snapshot = await firestore()
        .collection("Notifications")
        .where("userId", "==", user?.userId)
        .orderBy("createdAt", "desc")
        .startAfter(lastVisible)
        .limit(pageSize)
        .get();

      const newNotifications: NotificationType[] = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as NotificationType[];

      setNotifications((prev) => {
        const existingIds = new Set(prev.map((n) => n.id));
        const filtered = newNotifications.filter((n) => !existingIds.has(n.id));
        return [...prev, ...filtered];
      });

      if (!snapshot.empty) {
        setLastVisible(snapshot.docs[snapshot.docs.length - 1]);
      }

      if (snapshot.empty || snapshot.size < pageSize) {
        setAllDataFetched(true);
      }
    } catch (err) {
      console.error("Pagination fetch error:", err);
    } finally {
      setIsFetchingMore(false);
    }
  };

  return (
    <View style={styles.container}>
      <SafeAreaView />
      {isLoading ? (
        <ActivityIndicator
          size={"large"}
          color={colors.primary}
          style={{ marginTop: Dimensions.get("screen").height / 5 }}
        />
      ) : (
        <>
          <View style={{ marginBottom: 17 }} />
          <FlatList
            showsVerticalScrollIndicator={false}
            style={{ paddingHorizontal: 32 }}
            contentContainerStyle={{ paddingBottom: 16 }}
            ListEmptyComponent={() => <Text style={styles.emptyText}>No notifications yet</Text>}
            ListFooterComponent={() =>
              isFetchingMore ? (
                <ActivityIndicator size="small" color={colors.primary} style={{ marginVertical: 10 }} />
              ) : null
            }
            onEndReached={() => {
              if (hasScrolled && !allDataFetched) {
                fetchMoreNotifications();
              }
            }}
            onScrollBeginDrag={() => setHasScrolled(true)}
            onEndReachedThreshold={0.2}
            data={notifications}
            renderItem={({ item }) => (
              <Pressable
                style={styles.orderContainer}
                onPress={() => {
                  item.recipientId
                    ? item.recipientId &&
                      navigation.navigate("ChatDetails", {
                        recipientId: item.recipientId,
                      })
                    : item.title === "New Order Received!"
                    ? navigation.navigate("Management")
                    : item.title === "Order Place Successfully"
                    ? navigation.navigate("Orders")
                    : item.title === "Reminder: Return Borrowed Product"
                    ? navigation.navigate("Orders")
                    : null;
                }}
              >
                <View style={styles.checkOrderContainer}>
                  <Text style={styles.orderLabel}>
                    {formatDistanceToNow(item.createdAt.toDate(), {
                      addSuffix: true,
                    })}
                  </Text>
                </View>
                <Text style={styles.orderHeading}>{item.title}</Text>
                <Text style={styles.orderLabel}>{item.body}</Text>
              </Pressable>
            )}
            keyExtractor={(item) => item.id}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  orderContainer: {
    paddingHorizontal: 17,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 10,
    marginBottom: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },

  checkOrderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },

  orderHeading: {
    fontSize: 16,
    fontFamily: fonts.SemiBold,
    color: colors.black,
    marginVertical: 5,
  },

  orderLabel: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },

  emptyText: {
    fontSize: 20,
    fontFamily: fonts.SemiBold,
    color: colors.black,
    textAlign: "center",
    marginTop: Dimensions.get("screen").height / 5,
  },
});

export default Notification;
