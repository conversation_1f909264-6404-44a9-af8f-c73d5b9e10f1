import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  View,
} from 'react-native';
import React from 'react';
import {GoogleLoginStroke} from '../../assets/svg';
import {colors} from '../../utilities/theme';

interface GoogleLoginButtonProps {
  loading: boolean;
  onPress: () => void;
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  loading,
  onPress,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.button}
      disabled={loading}
      onPress={onPress}>
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator
            size="small"
            color={'#121212'}
            style={styles.loader}
          />
        </View>
      ) : (
        <>
          <View style={{width: 22}}>
            <GoogleLoginStroke width={22} height={22} />
          </View>
          <Text style={styles.text}>Continue with Google</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

export default GoogleLoginButton;

const styles = StyleSheet.create({
  button: {
    backgroundColor: colors.white,
    height: 42,
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderRadius: 10,
    flexDirection: 'row',
    paddingLeft: 10,
    gap: 16,
    borderWidth: 1,
    borderColor: '#E7E7E7',
  },
  text: {
    fontFamily: 'ProductSans-Medium',
    fontSize: 14,
    color: '#121212',
  },
  loader: {
    transform: [{translateX: -10}],
  },
  loaderContainer: {
    transform: [{scale: 0.8}],
    position: 'absolute',
    left: '50%',
  },
});
