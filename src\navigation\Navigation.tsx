import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { Linking } from "react-native";
import AuthStackNavigator from "./AuthNavigation";
import HomeStackNavigator from "./HomeNavigation";
import AdminStackNavigator from "./AdminNavigation";
import { useUser } from "../Hooks/UseContext";
import { MenuProvider } from "react-native-popup-menu";
import { TourGuideProvider } from "rn-tourguide";
import CustomToolTip from "../component/customToolTip/CustomToolTip";
import { DeeplinkHandlerHOC } from "../hoc";
import useNotification from "../Hooks/useNotification";
import NavigationService from "./NavigationService";
import messaging from "@react-native-firebase/messaging";
import { FirebaseMessagingTypes } from "@react-native-firebase/messaging";
import Toast from "react-native-toast-message";

// Deep linking configuration
const linking = {
  prefixes: ["voitto://", "https://voitto.app"],
  config: {
    screens: {
      // Auth Stack Screens
      SignIn: "signin",
      Register: "register",

      // Home Stack Screens (User)
      BottomTabs: {
        path: "home",
        screens: {
          Search: "dashboard",
        },
      },
      ProductDetails: "product/:id",
    },
  },
};

const Navigation = () => {
  const { user } = useUser();
  const { checkAndRequestPermission } = useNotification();
  useEffect(() => {
    if (user?.userId) {
      checkAndRequestPermission(user?.userId);
    }
  }, [user?.userId]);

  messaging().onMessage(async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
    const parseData = remoteMessage.data;
    const screen = parseData?.screen;
    const recipientId = parseData?.recipientId;

    if (remoteMessage.notification) {
      Toast.show({
        type: "success",
        text1:
          typeof remoteMessage.notification.title === "string"
            ? remoteMessage.notification.title
            : String(remoteMessage.notification.title ?? ""),
        text2:
          typeof remoteMessage.notification.body === "string"
            ? remoteMessage.notification.body
            : String(remoteMessage.notification.body ?? ""),
        onPress: () => {
          if (screen === "ChatDetails") {
            NavigationService.navigate(screen, { recipientId }); // ChatDetails, etc.
          } else if (screen === "Notification") {
            NavigationService.navigate("Notification"); // Don't pass recipientId
          } else {
            console.log("hello world");

            NavigationService.navigate("Notification"); // Final fallback
          }
          if (remoteMessage) {
            console.log("Remote Notification Listener>>>", remoteMessage.data);
          }
          // NavigationService.navigate("Notification");
          Toast.hide();
        },
        visibilityTime: 2000,
        swipeable: false,
      });
    } else {
      console.log("Remote message data is undefined or missing details.");
    }
  });

  return (
    <TourGuideProvider
      {...{ borderRadius: 16 }}
      tooltipComponent={CustomToolTip}
      labels={{
        skip: "Skip",
        previous: "Previous",
        next: "Next",
        finish: "Finish",
      }}
    >
      <MenuProvider>
        <NavigationContainer ref={(ref) => NavigationService.setTopLevelNavigator(ref)} linking={linking}>
          {user?.userId ? (
            user.userType === "Admin" ? (
              <AdminStackNavigator />
            ) : (
              <HomeStackNavigator />
            )
          ) : (
            <AuthStackNavigator />
          )}
        </NavigationContainer>
      </MenuProvider>
    </TourGuideProvider>
  );
};
export default DeeplinkHandlerHOC(Navigation);
