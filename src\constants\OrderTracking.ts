import { IOrderTracking } from "../interfaces/IOrders";

export const ORDER_TRACKING: IOrderTracking[] = [
  {
    title: "Order Placed",
    key: "order_placed",
    description: "Your order has been placed successfully.",
    completed: true,
    timestamp: null,
  },
  {
    title: "Order is being Processed",
    key: "order_processing",
    description: "Order is being processed. Will go under preparation.",
    completed: false,
    timestamp: null,
  },
  {
    title: "Expected Delivery",
    key: "order_expected",
    description: "Expected to be delivered within 5 business days.",
    completed: false,
    timestamp: null,
  },
  {
    title: "Delivered",
    key: "order_delivered",
    description: "Your order has been delivered.",
    completed: false,
    timestamp: null,
  },
];
