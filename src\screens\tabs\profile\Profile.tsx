import { StyleSheet, ScrollView } from "react-native";
import React, { useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import { ProfileContainer, ProfileItem } from "../../../component";
import { useUser } from "../../../Hooks/UseContext";
import { HelpIcon, LogoutIcon, Orders, ProfileSettingsIcon, SecurityIcon } from "../../../assets/svg";
import { colors } from "../../../utilities/theme";
import ConfirmationModal from "../../../model/ConfirmationModal";
import useAuth from "../../../Hooks/UseAuth";
type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList, "Profile">;

const Profile: React.FC<Props> = ({ navigation }) => {
  const { logOut } = useAuth();
  const { setUser, user } = useUser();
  const [isLogOutModel, setLogOutModal] = useState(false);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ProfileContainer onPress={() => navigation.navigate("EditProfile")} />

      {/* <ProfileItem title="Preferences" icon={ProfileSettingsIcon} onPress={() => navigation.navigate("Options")} /> */}
      <ProfileItem title="Orders" icon={Orders} onPress={() => navigation.navigate("Orders")} />
      {/* <ProfileItem
        title="Account Security"
        icon={SecurityIcon}
        showProgressbar={50}
      /> */}
      <ProfileItem title="Help" icon={HelpIcon} onPress={() => navigation.navigate("ContactUs")} />
      <ProfileItem title="Logout" icon={LogoutIcon} onPress={() => setLogOutModal(true)} />

      <ConfirmationModal
        isVisible={isLogOutModel}
        onClose={() => setLogOutModal(false)}
        onPress={() => {
          if (user?.userId) {
            logOut(user.userId);
          }
          setLogOutModal(false);
        }}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 32,
    backgroundColor: colors.white,
  },
});

export default Profile;
