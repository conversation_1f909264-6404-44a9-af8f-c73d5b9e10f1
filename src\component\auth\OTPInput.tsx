import {StyleSheet} from 'react-native';
import React from 'react';
import {OtpInput} from 'react-native-otp-entry';
import {colors, fonts} from '../../utilities/theme';

const OTPInput = ({setOTPCode}: {setOTPCode: (text: string) => void}) => {
  return (
    <OtpInput
      numberOfDigits={4}
      focusColor={colors.primary}
      focusStickBlinkingDuration={500}
      onTextChange={text => console.log(text)}
      onFilled={text => setOTPCode(text)}
      textInputProps={{
        accessibilityLabel: 'One-Time Password',
      }}
      theme={{
        containerStyle: styles.container,
        pinCodeContainerStyle: styles.pinCodeContainer,
        pinCodeTextStyle: styles.pinCodeText,
        focusedPinCodeContainerStyle: styles.activePinCodeContainer,
      }}
    />
  );
};

export default OTPInput;

const styles = StyleSheet.create({
  container: {
    marginTop: 80,
  },
  pinCodeContainer: {
    width: 70,
    height: 70,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: `${colors.primary}20`,
  },
  pinCodeText: {
    fontSize: 45,
    fontFamily: fonts.Bold,
    color: colors.primary,
  },
  activePinCodeContainer: {
    borderColor: colors.primary,
  },
});
