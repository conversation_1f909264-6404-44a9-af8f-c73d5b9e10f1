import { Linking, EmitterSubscription } from 'react-native';
import { parseDeepLink, isValidDeepLink, ParsedDeepLink } from './deepLinkUtils';
import NavigationService from '../navigation/NavigationService';

interface User {
  userId?: string;
  [key: string]: any;
}

/**
 * Handle deep link URL parsing and navigation
 */
export const handleDeepLink = (url: string, user: User): void => {
  if (!url || !isValidDeepLink(url)) return;

  console.log('Deep link received:', url);

  const parsedLink: ParsedDeepLink | null = parseDeepLink(url);
  if (!parsedLink || !parsedLink.screen) {
    console.log('Could not parse deep link:', url);
    return;
  }

  const { screen, params } = parsedLink;

  // Only allow ProductDetails screen via deep link
  if (screen === 'ProductDetails') {
    if (!user?.userId) {
      console.log('User not authenticated for ProductDetails');
      // Store the deep link to handle after login if needed
      return;
    }
    try {
      NavigationService.navigate('ProductDetails', params);
      console.log('Navigated to: ProductDetails with params:', params);
    } catch (error) {
      console.log('Error navigating to deep link:', error);
    }
  } else {
    // For any other screen, go to Search
    if (!user?.userId) {
      console.log('User not authenticated, cannot navigate to Search');
      return;
    }
    try {
      NavigationService.navigate('BottomTabs', { screen: 'Search' });
      console.log('Navigated to: Search');
    } catch (error) {
      console.log('Error navigating to Search:', error);
    }
  }
};

/**
 * Handle initial deep link when app is opened from a deep link
 */
export const handleInitialDeepLink = async (user: User): Promise<void> => {
  try {
    const initialUrl = await Linking.getInitialURL();
    if (initialUrl) {
      // Add a small delay to ensure navigation is ready
      setTimeout(() => handleDeepLink(initialUrl, user), 1000);
    }
  } catch (error) {
    console.log('Error getting initial URL:', error);
  }
};

/**
 * Set up deep link listener for when app is already running
 */
export const setupDeepLinkListener = (user: User): EmitterSubscription => {
  const subscription = Linking.addEventListener('url', ({ url }) => {
    handleDeepLink(url, user);
  });

  return subscription;
};

/**
 * Initialize deep linking functionality
 */
export const initializeDeepLinking = (user: User): () => void => {
  // Handle initial deep link
  handleInitialDeepLink(user);

  // Set up listener for incoming deep links
  const subscription = setupDeepLinkListener(user);

  // Return cleanup function
  return () => {
    subscription.remove?.();
  };
};
