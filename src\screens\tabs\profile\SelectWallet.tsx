import {ScrollView, StyleSheet, View} from 'react-native';
import React, {useState} from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {App<PERSON>utton, AuthHeader} from '../../../component';
import {colors} from '../../../utilities/theme';
import {CapitalBank, ChaseBank, CitizenBank, USBank} from '../../../assets/svg';
import OptionsItems from '../../../component/profile/OptionsItems';
type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'SelectWallet'
>;
const SelectWallet: React.FC<Props> = ({navigation}) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  // Handle item press
  const handlePress = (id: string) => {
    setSelectedOption(id);
  };
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
        }}
        showsVerticalScrollIndicator={false}>
        {/* <View style={{paddingHorizontal: 32}}>
          <AuthHeader
            backArrow={true}
            onPress={() => navigation.goBack()}
            title="Select a Wallet"
          />
        </View> */}

        <View style={{marginBottom: 50}} />
        <OptionsItems
          title="PayPal"
          icon={USBank}
          selected={selectedOption === 'PayPal'}
          onPress={() => handlePress('PayPal')}
        />
        <OptionsItems
          title="Stripe"
          icon={CapitalBank}
          selected={selectedOption === 'Stripe'}
          onPress={() => handlePress('Stripe')}
        />
        <OptionsItems
          title="Payoneer"
          icon={CitizenBank}
          selected={selectedOption === 'Payoneer'}
          onPress={() => handlePress('Payoneer')}
        />
        <OptionsItems
          title="Chase"
          icon={ChaseBank}
          selected={selectedOption === 'Chase'}
          onPress={() => handlePress('Chase')}
        />

        <AppButton
          title="Confirm"
          customStyle={{marginHorizontal: 32}}
          onPress={() =>
            navigation.navigate('CongratsProfile', {
              title: 'Wallet has been added Successfully',
            } as any)
          }
        />
      </ScrollView>
    </View>
  );
};

export default SelectWallet;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});
