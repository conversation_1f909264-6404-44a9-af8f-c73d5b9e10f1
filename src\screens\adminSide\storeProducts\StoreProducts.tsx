import { StyleSheet, View, FlatList, ActivityIndicator, Dimensions, Text, TouchableOpacity } from "react-native";
import React, { useEffect, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";
import { AppButton } from "../../../component";
import { appStyles, colors, fonts } from "../../../utilities/theme";
import AdminStoreItem from "../../../component/adminCommon/AdminStoreItem";
import { IProducts } from "../../../interfaces/Products";
import firestore from "@react-native-firebase/firestore";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import DeleteConfimationModal from "../../../model/DeleteConfimationModal";
import { BackIcon, LeftIcon, NotificationIcon, RightArrow } from "../../../assets/svg";
import useNotification from "../../../Hooks/useNotification";
import { useUser } from "../../../Hooks/UseContext";
type Props = NativeStackScreenProps<AdminBottomTabParamlist & AdminStackParamsList, "StoreProducts">;

const StoreProducts: React.FC<Props> = ({ navigation }) => {
  const { user } = useUser();
  const [products, setProducts] = useState<IProducts[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);
  const { unreadCount, markNotificationsAsRead } = useNotification();

  const [selectedProduct, setSelectedProduct] = useState<IProducts | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    try {
      const unsubscribe = firestore()
        .collection("Products")
        .orderBy("createdAt", "desc")
        .onSnapshot(
          (snapshot) => {
            setProductsLoading(false);

            const _products = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            })) as IProducts[];

            setProducts(_products);
          },
          (error) => {
            console.log("Error while fetching all products", error);
            setProductsLoading(false);
          },
        );
      return () => unsubscribe();
    } catch (error) {
      console.log("Error while fetching all products", error);
      setProductsLoading(false);
    } finally {
      setProductsLoading(false);
    }
  }, []);

  const deleteProduct = () => {
    if (selectedProduct) {
      firestore()
        .collection("Products")
        .doc(selectedProduct.id)
        .delete()
        .then(() => {
          setProducts((prevProducts) => prevProducts.filter((item) => item.id !== selectedProduct.id));
          setIsVisible(false);
        })
        .catch((error) => {
          console.error("Error deleting product: ", error);
        });
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <View style={{ marginLeft: 32, marginTop: 4 }}>
          <LeftIcon />
        </View>
      ),
      headerTitle: "",
      headerRight: () => (
        <TouchableOpacity
          style={styles.notificationContainer}
          onPress={() => {
            user?.userId && markNotificationsAsRead(user?.userId);
            navigation.navigate("Notification");
          }}
        >
          {unreadCount > 0 ? (
            <View style={styles.counterContainer}>
              <Text style={styles.counterText}>{unreadCount}</Text>
            </View>
          ) : null}
          <NotificationIcon />
        </TouchableOpacity>
      ),
    });
  }, [unreadCount]);
  return (
    <View style={styles.container}>
      {productsLoading ? (
        <ActivityIndicator size="large" color={colors.primary} style={styles.loadingContainer} />
      ) : (
        <FlatList
          contentContainerStyle={styles.contentContainerStyle}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <Text style={appStyles.emptyStateText}>No products available. Please add a product to see it here.</Text>
          }
          data={products}
          renderItem={({ item }) => (
            <AdminStoreItem
              heading={item.title}
              title={item.subTitle}
              imageProp={item.image}
              btnText="BORROWED"
              onPressRemove={() => {
                setSelectedProduct(item);
                setIsVisible(true);
              }}
              onPressEdit={() =>
                navigation.navigate("AddProducts", {
                  product: item,
                })
              }
            />
          )}
          keyExtractor={(item) => item.id}
        />
      )}
      <View style={styles.bottomContainer}>
        <AppButton title="Add New Product" onPress={() => navigation.navigate("AddProducts")} />
      </View>
      <DeleteConfimationModal
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
        onPress={() => deleteProduct()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  iconContainer: {
    width: 34,
    height: 34,
    flexDirection: "row",
    position: "absolute",
    left: 0,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,

    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },

  bottomContainer: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    backgroundColor: colors.white,
    paddingHorizontal: 28,
    paddingVertical: 16,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    elevation: 5,
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowRadius: 15,
    shadowOffset: { width: 0, height: -6 },
  },
  loadingContainer: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainerStyle: {
    paddingTop: 20,
    paddingHorizontal: 28,
    paddingBottom: 90,
  },
  notificationContainer: {
    backgroundColor: colors.white,
    width: 48,
    height: 48,
    borderRadius: 18,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    justifyContent: "center",
    marginRight: 32,
    marginTop: 4,
  },
  counterContainer: {
    position: "absolute",
    right: -3,
    top: -2,
    width: 18,
    height: 18,
    borderRadius: 20,
    backgroundColor: colors.primary,
    zIndex: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  counterText: {
    fontSize: 12,
    color: colors.white,
    fontFamily: fonts.Medium,
  },
});

export default StoreProducts;
