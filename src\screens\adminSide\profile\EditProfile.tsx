import {
  ActivityIndicator,
  Alert,
  Image,
  Text,
  KeyboardAvoidingView,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  Keyboard,
  TextInput,
  FlatList,
  TouchableWithoutFeedback,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { AppButton, AuthHeader, ProfileInput } from "../../../component";
import { appStyles, colors } from "../../../utilities/theme";
import { images } from "../../../assets/images";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import firestore, { FieldPath } from "@react-native-firebase/firestore";
import storage from "@react-native-firebase/storage";
import { showToast } from "../../../helper/toast";
import ImagePickerModal from "../../../model/ImagePickerModal";
import { useUser } from "../../../Hooks/UseContext";
import { useFormik } from "formik";
import * as Yup from "yup";
import useAuth from "../../../Hooks/UseAuth";
import { IUser } from "../../../interfaces/IUser";
import { ProfileEdit } from "../../../assets/svg";
import parsePhoneNumberFromString from "libphonenumber-js";
import { CountryCode } from "react-native-country-picker-modal";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

type LocationItem = {
  label: string;
  value: string; // The value that corresponds to the place id
};
type Props = NativeStackScreenProps<AdminBottomTabParamlist & AdminStackParamsList, "EditProfile">;

const EditProfile: React.FC<Props> = ({ navigation }) => {
  const { setUser, user } = useUser();
  const { fetchUser } = useAuth();
  const [isModalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<IUser>();
  const [uploadImg, setUploadImg] = useState(false);

  const [fetchLoading, setFetchLoading] = useState(false);
  useEffect(() => {
    setFetchLoading(true);
    const fetchUserData = async () => {
      if (user?.userId) {
        try {
          const userData = await fetchUser(user.userId);
          if (userData) {
            userData.originalPhoneNumber = userData.phoneNumber;

            // Remove the first two characters (e.g., "+1", "92", etc.)
            userData.phoneNumber = userData.phoneNumber?.slice(3) || "";
          }

          setUserData(userData as IUser);
          setFetchLoading(false);
        } catch (error) {
          console.error("Error fetching user data:", error);
        } finally {
          setFetchLoading(false);
        }
      }
    };

    fetchUserData();
  }, [user?.userId]);

  const validationSchema = Yup.object().shape({
    name: Yup.string().max(30, "Max 30 characters"),
    location: Yup.string().max(30, "Max 30 characters"),
    address: Yup.string().max(30, "Max 30 characters"),
    phoneNumber: Yup.string()
      .required("Phone number is required")
      .matches(/^\+?[0-9]{8,15}$/, "Phone number must be 8–15 digits and can start with +"),
  });

  const formik = useFormik({
    initialValues: {
      name: userData?.name,
      location: userData?.location,
      address: userData?.address,
      phoneNumber: userData?.phoneNumber,
      profileImage: userData?.profileImage,
      defaultCountry: (parsePhoneNumberFromString(userData?.originalPhoneNumber || "")?.country || "US") as CountryCode,
    },

    enableReinitialize: true,
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      await handleSaveChanges(values as any);
    },
  });

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };

  const onGalleryPress = async () => {
    const result = await launchImageLibrary({
      mediaType: "photo",
      quality: 0.7,
    });

    if (result.didCancel || result.errorCode) {
      toggleModal();
      return;
    }

    if (result.assets && result.assets.length > 0) {
      const selectedImage = result.assets[0];
      if (selectedImage.uri) {
        formik.setFieldValue("profileImage", selectedImage.uri);
        setUploadImg(true);
        toggleModal();
      }
    }
  };

  const requestCameraPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA);
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn(err);
      return false;
    }
  };

  const onCameraPress = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert("Camera permission is required to take pictures.");
      return;
    }

    const result = await launchCamera({
      mediaType: "photo",
      quality: 0.7,
    });

    if (result.didCancel || result.errorCode) {
      toggleModal();
      return;
    }

    if (result.assets && result.assets.length > 0) {
      const capturedImage = result.assets[0];
      if (capturedImage.uri) {
        formik.setFieldValue("profileImage", capturedImage.uri);
        setUploadImg(true);
        toggleModal();
      }
    }
  };

  const uploadImage = async () => {
    setIsLoading(true);
    try {
      const filePath = `Users/${user?.userId}/profileImage/${user?.userId}_img`;
      const reference = storage().ref(filePath);
      await reference.putFile(formik.values.profileImage || "");
      const url = await reference.getDownloadURL();
      return url;
    } catch (error) {
      showToast("Error uploading image");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const updateUserDetails = async (values: IUser) => {
    try {
      await firestore().collection("Users").doc(user?.userId).update(values);
    } catch (error) {
      console.error("Error updating user details:", error);
      showToast("Could not update user details!");
    }
  };

  const handleSaveChanges = async (values: IUser) => {
    const uploadedUrl = uploadImg && (await uploadImage());
    const payload = {
      name: values.name || "",
      location: values.location || "",
      address: values.address || "",
      phoneNumber: values.phoneNumber || "",
      profileImage: uploadedUrl || userData?.profileImage || "",
      userId: user?.userId || "",
      email: user?.email || "",
    };
    setIsLoading(true);
    await updateUserDetails(payload);
    setIsLoading(false);
    setUser({ ...user, ...payload });
    showToast("User details updated successfully!", "Success", "success");
    navigation.goBack();
  };

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [locations, setLocations] = useState<LocationItem[]>([]);

  const [activeField, setActiveField] = useState<"location" | "address" | null>(null);

  const fetchLocationSuggestions = (text: string, field: string) => {
    if (text.length > 2) {
      fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&key=AIzaSyBCPC9bZ6ioVL_T2MCpvy9OuANBJf1KXdc`,
      )
        .then((response) => response.json())
        .then((data) => {
          const places = data.predictions.map((prediction: any) => ({
            label: prediction.description,
            value: prediction.place_id,
          }));

          if (field === "location") {
            setLocations(places);
          }
          setDropdownOpen(true);
        })
        .catch((error) => {
          console.error("Error fetching location data: ", error);
        });
    } else {
      setDropdownOpen(false);
      if (field === "location") {
        setLocations([]);
      }
    }
  };

  const handleLocationSelect = (item: LocationItem) => {
    formik.setFieldValue("location", item.label);
    setDropdownOpen(false);
  };

  const handleTextChange = (field: "location", text: string) => {
    formik.setFieldValue(field, text);
    setActiveField(field);
    fetchLocationSuggestions(text, field);
  };

  const closeDropdown = () => {
    setDropdownOpen(false);
    Keyboard.dismiss();
    setActiveField(null);
  };
  return (
    <TouchableWithoutFeedback onPress={closeDropdown}>
      <View style={styles.container}>
        {/* <SafeAreaView />
        <AuthHeader
          backArrow={true}
          onPress={() => navigation.goBack()}
          title="Edit Profile"
        /> */}
        <KeyboardAwareScrollView
          contentContainerStyle={styles.contentContainerStyle}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
        >
          <View style={styles.profileImgContainer}>
            {fetchLoading ? (
              // <ActivityIndicator size="large" color={colors.primary} />
              <View
                style={{
                  width: 122,
                  height: 122,
                  borderRadius: 100,
                  backgroundColor: colors.gray[300],
                }}
              />
            ) : (
              <Image
                style={{ width: 112, height: 112, borderRadius: 100 }}
                source={formik.values.profileImage ? { uri: formik.values.profileImage } : images.Avatar}
              />
            )}
            <TouchableOpacity onPress={toggleModal} style={styles.iconContainer} activeOpacity={0.6}>
              <ProfileEdit height={22} width={22} />
            </TouchableOpacity>
          </View>
          {fetchLoading ? (
            <ActivityIndicator size="large" color={colors.primary} style={{ marginTop: 60 }} />
          ) : (
            <View>
              <ProfileInput
                label="Name"
                placeholder="Maeve Oggy"
                value={formik.values.name}
                onChangeText={formik.handleChange("name")}
                onBlur={formik.handleBlur("name")}
                onFocusInput={() => setDropdownOpen(false)}
                showImage={true}
                errorMessage={formik.touched.name && formik.errors.name}
              />
              <View style={{ marginBottom: -16 }}>
                <ProfileInput
                  label="Location"
                  placeholder="US(United State)"
                  value={formik.values.location}
                  onChangeText={(text) => handleTextChange("location", text)}
                  onBlur={() => {
                    setDropdownOpen(false);
                    formik.handleBlur("location");
                  }}
                  onFocusInput={() => setDropdownOpen(false)}
                  showImage={true}
                  errorMessage={formik.touched.location && formik.errors.location}
                />
              </View>
              {dropdownOpen && activeField === "location" && locations.length > 0 && (
                <FlatList
                  data={locations.slice(0, 3)}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                  keyExtractor={(item) => item.value}
                  renderItem={({ item }) => (
                    <TouchableOpacity style={styles.suggestionItem} onPress={() => handleLocationSelect(item)}>
                      <Text style={styles.suggestionText}>{item.label}</Text>
                    </TouchableOpacity>
                  )}
                  style={styles.suggestionList}
                />
              )}
              <View style={{ marginBottom: 16 }} />
              <ProfileInput
                label="Address"
                placeholder="321 Street/WA"
                value={formik.values.address}
                onChangeText={formik.handleChange("address")}
                onBlur={formik.handleBlur("address")}
                onFocusInput={() => setDropdownOpen(false)}
                showImage={true}
                errorMessage={formik.touched.address && formik.errors.address}
              />

              <ProfileInput
                label="Phone Number"
                placeholder="+000 000 00000"
                value={formik.values.phoneNumber}
                onChangeText={formik.handleChange("phoneNumber")}
                onBlur={() => {
                  setDropdownOpen(false);
                  formik.handleBlur("phoneNumber");
                }}
                defaultCountryValue={formik.values.defaultCountry}
                showImage={true}
                keyboardType="numeric"
                phonePicker={true}
                onFocusInput={() => setDropdownOpen(false)}
                errorMessage={formik.touched.phoneNumber && formik.errors.phoneNumber}
              />
              <AppButton
                title="Save Changes"
                onPress={formik.handleSubmit}
                customStyle={{ marginTop: 16 }}
                isLoading={isLoading}
              />
            </View>
          )}
        </KeyboardAwareScrollView>
        {/* Modal for Image Picker */}
        <ImagePickerModal
          isVisible={isModalVisible}
          onClose={toggleModal}
          onCameraPress={onCameraPress}
          onGalleryPress={onGalleryPress}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default EditProfile;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 32,
    backgroundColor: colors.white,
  },

  contentContainerStyle: {
    // flexGrow: 1,
    marginHorizontal: 4,
    paddingBottom: 20,
  },

  profileImgContainer: {
    backgroundColor: colors.white,
    marginBottom: 10,
    alignItems: "center",
    alignSelf: "center",
    justifyContent: "center",
    borderRadius: 25,
    marginTop: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    width: 145,
    height: 145,
  },
  iconContainer: {
    position: "absolute",
    zIndex: 2,
    right: 22,
    bottom: 16,
    width: 34,
    height: 34,
    backgroundColor: colors.white,
    borderRadius: 34 / 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    alignItems: "center",
    justifyContent: "center",
  },

  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.6)",
  },
  suggestionList: {
    backgroundColor: "#fff",
    maxHeight: 170,
  },
  suggestionItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  suggestionText: {
    fontSize: 14,
    color: "#333",
    zIndex: 29,
  },
  error: {
    fontSize: 12,
    color: "red",
    marginTop: 3,
  },
});
