import {ScrollView, StyleSheet, View} from 'react-native';
import React, {useState} from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {App<PERSON>utton, AuthHeader} from '../../../component';
import {colors} from '../../../utilities/theme';
import {CapitalBank, ChaseBank, CitizenBank, USBank} from '../../../assets/svg';
import OptionsItems from '../../../component/profile/OptionsItems';
type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'SelectBank'
>;
const SelectBank: React.FC<Props> = ({navigation}) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  // Handle item press
  const handlePress = (id: string) => {
    setSelectedOption(id);
  };
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
        }}
        showsVerticalScrollIndicator={false}>
        <AuthHeader
          backArrow={true}
          onPress={() => navigation.goBack()}
          title="Select a Bank"
          titleStyle={{marginLeft: 30}}
          arrowContainerStyle={{marginHorizontal: 32}}
        />
        <View style={{marginBottom: 50}} />

        <OptionsItems
          title="U.S Bank"
          icon={USBank}
          selected={selectedOption === 'U.S Bank'}
          onPress={() => handlePress('U.S Bank')}
        />
        <OptionsItems
          title="Capital One"
          icon={CapitalBank}
          selected={selectedOption === 'Capital One'}
          onPress={() => handlePress('Capital One')}
        />
        <OptionsItems
          title="Citizens Bank"
          icon={CitizenBank}
          selected={selectedOption === 'Citizens Bank'}
          onPress={() => handlePress('Citizens Bank')}
        />
        <OptionsItems
          title="Chase"
          icon={ChaseBank}
          selected={selectedOption === 'Chase'}
          onPress={() => handlePress('Chase')}
        />

        <AppButton
          title="Confirm"
          customStyle={{marginTop: 20, marginHorizontal: 32}}
          onPress={() => navigation.navigate('Options')}
        />
      </ScrollView>
    </View>
  );
};

export default SelectBank;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingHorizontal: 32,
    backgroundColor: colors.white,
  },
});
