import {StyleSheet, View, TouchableOpacity, Text, Image} from 'react-native';
import React from 'react';
import {colors, fonts} from '../../utilities/theme';
import {BackIcon} from '../../assets/svg';
import {images} from '../../assets/images';

interface Props {
  onPress?: () => void;
  name?: string;
  userType: 'Admin' | 'User';
  image?: string;
}

const ChatHeader: React.FC<Props> = ({onPress, name, image, userType}) => {
  return (
    <View style={styles.mainContainer}>
      <TouchableOpacity style={styles.backButton} onPress={onPress}>
        <BackIcon />
      </TouchableOpacity>

      <Image
        style={styles.profileImage}
        source={image ? {uri: image} : images.Avatar}
      />

      <View style={{flex: 1}}>
        <Text style={styles.title}>{name}</Text>
        <Text style={styles.subTitle}>
          {userType === 'Admin' ? 'Support' : 'Available'}
        </Text>
      </View>
    </View>
  );
};

export default ChatHeader;

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingVertical: 16,
  },
  backButton: {
    width: 34,
    height: 34,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginHorizontal: 16,
    backgroundColor: colors.gray[100],
  },
  title: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.gray[50],
  },
});
