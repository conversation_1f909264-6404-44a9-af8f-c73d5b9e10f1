import { useState, useCallback } from "react";
import { initPaymentSheet } from "@stripe/stripe-react-native";
import functions from "@react-native-firebase/functions";

type useStripeParams = {
  customerId: string;
  amount: number;
};

const useStripe = ({ customerId, amount }: useStripeParams) => {
  const [initialLoading, setInitialLoading] = useState(false);
  const [isError, setIsError] = useState(false);

  // 1. Fetch params from Firebase Function
  const fetchPaymentSheetParams = useCallback(async () => {
    setInitialLoading(true);
    try {
      const res = await functions().httpsCallable("oneTimePayment")({
        customerId,
        amount,
        currency: "usd",
      });

      const { paymentIntent, ephemeralKey, customer } = res.data as {
        paymentIntent: string;
        ephemeralKey: string;
        customer: string;
      };

      return { paymentIntent, ephemeralKey, customer };
    } catch (error) {
      console.error("Error fetching Payment Sheet params:", error);
      return null;
      throw error;
    } finally {
      setInitialLoading(false);
    }
  }, [customerId, amount]);

  // 2. Initialize the Payment Sheet
  // const initializePaymentSheet = useCallback(async () => {
  //   try {
  //     const {paymentIntent, ephemeralKey, customer} =
  //       await fetchPaymentSheetParams();

  //     const {error} = await initPaymentSheet({
  //       customerId: customer,
  //       customerEphemeralKeySecret: ephemeralKey,
  //       paymentIntentClientSecret: paymentIntent,
  //       merchantDisplayName: 'Merchant Name',
  //       allowsDelayedPaymentMethods: true,
  //       returnURL: 'yourapp://payment-success',
  //     });

  //     if (error) {
  //       console.error('initPaymentSheet error:', error);
  //       setIsError(true);
  //       return false;
  //     }

  //     return true;
  //   } catch (error) {
  //     setIsError(true);
  //     return false;
  //   }
  // }, [fetchPaymentSheetParams]);
  const initializePaymentSheet = useCallback(async () => {
    try {
      const res = await fetchPaymentSheetParams();

      if (!res) {
        setIsError(true);
        return { success: false, paymentIntentId: null };
      }

      const { paymentIntent, ephemeralKey, customer } = res;
      const paymentIntentId = paymentIntent.split("_secret")[0]; // Extract the id

      const { error } = await initPaymentSheet({
        customerId: customer,
        customerEphemeralKeySecret: ephemeralKey,
        paymentIntentClientSecret: paymentIntent,
        merchantDisplayName: "Merchant Name",
        allowsDelayedPaymentMethods: true,
        returnURL: "yourapp://stripe-redirect", // Required for redirect-based payments
      });

      if (error) {
        console.error("initPaymentSheet error:", error);
        setIsError(true);
        return { success: false, paymentIntentId: null };
      }

      return { success: true, paymentIntentId }; // Return it here
    } catch (error) {
      setIsError(true);
      return { success: false, paymentIntentId: null };
    }
  }, [fetchPaymentSheetParams]);

  return {
    initializePaymentSheet,
    initialLoading,
    isError,
    fetchPaymentSheetParams,
  };
};

export default useStripe;
