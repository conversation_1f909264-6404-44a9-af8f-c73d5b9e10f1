import {StyleSheet, View, Text, Image, TouchableOpacity} from 'react-native';
import React from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {colors, fonts} from '../../../utilities/theme';
import OptionsItems from '../../../component/profile/OptionsItems';
import {DebitCardIcon, FavouriteBank, LanguageIcon} from '../../../assets/svg';
import {useUser} from '../../../Hooks/UseContext';
import {images} from '../../../assets/images';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'Options'
>;
const Options: React.FC<Props> = ({navigation}) => {
  const {user} = useUser();
  return (
    <View style={styles.container}>
      <View style={{marginBottom: 35}} />

      {/* <OptionsItems
        title="Add a Bank Account"
        onPress={() => navigation.navigate('AddBank')}
        icon={BankIcon}
      /> */}
      <OptionsItems
        title="Add a credit or debit card"
        icon={DebitCardIcon}
        onPress={() => navigation.navigate('AddCard', {productId: []})}
      />
      {/* <OptionsItems
        title="Add a Digital Wallet"
        icon={DigitalWallet}
        onPress={() => navigation.navigate('AddWallet')}
      /> */}
      <OptionsItems
        title="Wishlist & Favorites"
        icon={FavouriteBank}
        onPress={() => navigation.navigate('Wishlist')}
      />
      <OptionsItems title="Language" icon={LanguageIcon} />
      {/* <OptionsItems title="Currency" icon={CurrencyIcon} /> */}
      <TouchableOpacity
        style={[styles.bottomContainer]}
        onPress={() => navigation.navigate('ChatDetails')}>
        <Text style={styles.bottomText}>
          Hey, {user?.name ? user.name.split(' ')[0] : ''}
        </Text>
        <Image
          style={{width: 50, height: 50, borderRadius: 100}}
          source={user?.profileImage ? {uri: user.profileImage} : images.Avatar}
        />
      </TouchableOpacity>
    </View>
  );
};

export default Options;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  bottomContainer: {
    paddingVertical: 5,
    paddingHorizontal: 20,
    position: 'absolute',
    backgroundColor: colors.white,
    right: 28,
    bottom: 25,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  bottomText: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: colors.gray[250],
  },
});
