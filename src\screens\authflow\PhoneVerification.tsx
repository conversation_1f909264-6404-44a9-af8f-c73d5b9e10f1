import React from 'react';
import {StyleSheet, View, Text, TextInput, SafeAreaView} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {AppButton} from '../../component';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {FlagIconInactive, FlagIconActive} from '../../assets/svg';

type Props = NativeStackScreenProps<AuthStackParamList, 'PhoneVerification'>;

const PhoneVerification: React.FC<Props> = ({navigation}) => {
  const validationSchema = Yup.object().shape({
    phoneNumber: Yup.string()
      .required('Phone number is required')
      .matches(/^[0-9+ ]*$/, 'Invalid phone number format'),
  });

  const formik = useFormik({
    initialValues: {
      phoneNumber: '',
    },
    validationSchema: validationSchema,
    onSubmit: values => {
      navigation.navigate('OTP', {title: 'Account Created Successfully'});
    },
  });

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <View>
        <Text style={appStyles.titleStyle}>Register</Text>

        <Text style={styles.titleLabelStyle}>
          Please enter{' '}
          <Text style={styles.titleLabelSpan}>your phone number,</Text> so we
          can verify you.
        </Text>

        <Text style={styles.inputLabel}>Enter your phone number</Text>
        <View
          style={[
            styles.inputContainer,
            {
              backgroundColor: formik.values.phoneNumber
                ? '#fcf6f4'
                : '#FFFFFF',
            },
          ]}>
          {formik.values.phoneNumber ? (
            <FlagIconInactive />
          ) : (
            <FlagIconActive />
          )}
          <View style={styles.verticalDivider}></View>
          <TextInput
            style={[styles.textInput]}
            placeholder="+00 000 0000"
            placeholderTextColor={'#555555'}
            keyboardType="number-pad"
            onChangeText={formik.handleChange('phoneNumber')}
            onBlur={formik.handleBlur('phoneNumber')}
            value={formik.values.phoneNumber}
          />
        </View>
        {formik.touched.phoneNumber && formik.errors.phoneNumber ? (
          <Text style={styles.errorText}>{formik.errors.phoneNumber}</Text>
        ) : null}

        <AppButton
          title="Next"
          onPress={formik.handleSubmit}
          customStyle={{marginTop: 40}}
          isLoading={false}
          disabled={!formik.isValid || !formik.dirty}
        />
      </View>
    </View>
  );
};

export default PhoneVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
  },

  titleLabelStyle: {
    fontSize: 16,
    fontFamily: fonts.Regular,
    color: colors.black,
    marginVertical: 40,
  },

  titleLabelSpan: {
    fontSize: 16,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  inputLabel: {
    fontSize: 16,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginBottom: 17,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 57,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,
    elevation: 3,
  },
  verticalDivider: {
    borderRightWidth: 1,
    borderColor: '#000000',
    height: '60%',
    marginLeft: 16,
    marginRight: 25,
  },
  textInput: {
    flex: 1,
    height: '100%',
    fontSize: 17,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  errorText: {
    color: 'red',
    marginTop: 5,
    fontSize: 14,
    fontFamily: fonts.Regular,
  },
});
