import messaging from "@react-native-firebase/messaging";
import { useEffect } from "react";
import { PermissionsAndroid, Platform } from "react-native";
import { useUser } from "../Hooks/UseContext";
import NavigationService from "../navigation/NavigationService";

export const DeeplinkHandlerHOC = (RootNavigator) => {
  const MessageHandlerComponent = () => {
    const { setFcmToken } = useUser();
    const getFCMToken = async () => {
      try {
        const fcmToken = await messaging().getToken();
        if (fcmToken) {
          setFcmToken(fcmToken);
        }
      } catch (error) {
        console.log("Error in fetching FCM token:", error);
      }
    };

    const notificationListener = async () => {
      messaging().onNotificationOpenedApp((remoteMessage) => {
        const parseData = remoteMessage.data;
        const screen = parseData.screen;
        const recipientId = parseData.recipientId;

        if (screen === "ChatDetails") {
          NavigationService.navigate(screen, { recipientId }); // ChatDetails, etc.
        } else if (screen === "Notification") {
          NavigationService.navigate("Notification"); // Don't pass recipientId
        } else {
          console.log("hello world");

          NavigationService.navigate("Notification"); // Final fallback
        }
        if (remoteMessage) {
          console.log("Remote Notification Listener>>>", remoteMessage.data);
        }
      });

      messaging().setBackgroundMessageHandler(async (remoteMessage) => {
        console.log("Remote message", remoteMessage.data);
        const parseData = remoteMessage.data;
        const screen = parseData.screen;
        const recipientId = parseData.recipientId;

        // if (screen === "ChatDetails") {
        //   NavigationService.navigate(screen, { recipientId }); // ChatDetails, etc.
        // } else if (screen === "Notification") {
        //   NavigationService.navigate("Notification"); // Don't pass recipientId
        // } else {
        //   console.log("hello world");

        //   NavigationService.navigate("Notification"); // Final fallback
        // }
      });

      messaging()
        .getInitialNotification()
        .then((remoteMessage) => {
          if (remoteMessage) {
            console.log("Notification caused app to open from quit state:", remoteMessage.notification);
          }
        });

      messaging().onMessage(async (remoteMessage) => {
        console.log("Notification received in foreground:", remoteMessage.data);
      });
    };

    const requestAndroidNotificationPermission = async () => {
      if (Platform.OS === "android") {
        const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log("Notification permission granted.");
          return true;
        } else {
          console.log("Notification permission denied.");
          return false;
        }
      }
      return true;
    };

    const requestUserPermission = async () => {
      if (Platform.OS === "android") {
        const hasPermission = await requestAndroidNotificationPermission();
        if (!hasPermission) return;
      }

      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log("FCM permission granted");
        getFCMToken();
      } else {
        console.log("FCM permission denied");
      }
    };

    useEffect(() => {
      requestUserPermission();
      notificationListener();
    }, []);

    return <RootNavigator />;
  };
  return MessageHandlerComponent;
};
