import messaging from "@react-native-firebase/messaging";
import { useEffect } from "react";
import { PermissionsAndroid, Platform, Linking } from "react-native";
import { useUser } from "../Hooks/UseContext";
import NavigationService from "../navigation/NavigationService";
import { parseDeepLink, isValidDeepLink } from "../utilities/deepLinkUtils";

export const DeeplinkHandlerHOC = (RootNavigator) => {
  const MessageHandlerComponent = () => {
    const { setFcmToken, user } = useUser();
    const getFCMToken = async () => {
      try {
        const fcmToken = await messaging().getToken();
        if (fcmToken) {
          setFcmToken(fcmToken);
        }
      } catch (error) {
        console.log("Error in fetching FCM token:", error);
      }
    };

    // Handle deep link URL parsing and navigation
    const handleDeepLink = (url) => {
      if (!url || !isValidDeepLink(url)) return;

      console.log("Deep link received:", url);

      const parsedLink = parseDeepLink(url);
      if (!parsedLink || !parsedLink.screen) {
        console.log('Could not parse deep link:', url);
        return;
      }

      const { screen, params } = parsedLink;

      // Check if user needs to be authenticated for this screen
      const authRequiredScreens = [
        'ProductDetails', 'ChatDetails', 'OrderTracking', 'AdminOrderDetails',
        'Notification', 'EditProfile', 'ContactUs', 'Orders', 'Options', 'AddCard',
        'Home', 'Search', 'Profile', 'Cart', 'AdminProfile', 'StoreProducts',
        'StoreCategories', 'Analytics', 'Management', 'AddProducts', 'AddCategories',
        'UserListChat'
      ];

      const publicScreens = ['SignIn', 'Register'];

      if (authRequiredScreens.includes(screen) && !user?.userId) {
        console.log('User not authenticated for screen:', screen);
        // Store the deep link to handle after login
        // You could store this in AsyncStorage or context
        return;
      }

      if (publicScreens.includes(screen) && user?.userId) {
        console.log('User already authenticated, ignoring auth screen:', screen);
        return;
      }

      // Handle admin-specific screens
      const adminScreens = [
        'AdminOrderDetails', 'AdminProfile', 'StoreProducts', 'StoreCategories',
        'Analytics', 'Management', 'AddProducts', 'AddCategories', 'UserListChat'
      ];

      if (adminScreens.includes(screen) && user?.userType !== 'Admin') {
        console.log('User is not admin for screen:', screen);
        return;
      }

      // Navigate to the screen
      try {
        NavigationService.navigate(screen, params);
        console.log('Navigated to:', screen, 'with params:', params);
      } catch (error) {
        console.log('Error navigating to deep link:', error);
      }
    };

    // Handle initial deep link when app is opened from a deep link
    const handleInitialDeepLink = async () => {
      try {
        const initialUrl = await Linking.getInitialURL();
        if (initialUrl) {
          // Add a small delay to ensure navigation is ready
          setTimeout(() => handleDeepLink(initialUrl), 1000);
        }
      } catch (error) {
        console.log('Error getting initial URL:', error);
      }
    };

    // Handle deep links when app is already running
    const handleDeepLinkListener = () => {
      const subscription = Linking.addEventListener('url', ({ url }) => {
        handleDeepLink(url);
      });

      return subscription;
    };

    const notificationListener = async () => {
      messaging().onNotificationOpenedApp((remoteMessage) => {
        const parseData = remoteMessage.data;
        const screen = parseData.screen;
        const recipientId = parseData.recipientId;

        if (screen === "ChatDetails") {
          NavigationService.navigate(screen, { recipientId }); // ChatDetails, etc.
        } else if (screen === "Notification") {
          NavigationService.navigate("Notification"); // Don't pass recipientId
        } else {
          console.log("hello world");

          NavigationService.navigate("Notification"); // Final fallback
        }
        if (remoteMessage) {
          console.log("Remote Notification Listener>>>", remoteMessage.data);
        }
      });

      messaging().setBackgroundMessageHandler(async (remoteMessage) => {
        console.log("Remote message", remoteMessage.data);
        const parseData = remoteMessage.data;
        const screen = parseData.screen;
        const recipientId = parseData.recipientId;

        // if (screen === "ChatDetails") {
        //   NavigationService.navigate(screen, { recipientId }); // ChatDetails, etc.
        // } else if (screen === "Notification") {
        //   NavigationService.navigate("Notification"); // Don't pass recipientId
        // } else {
        //   console.log("hello world");

        //   NavigationService.navigate("Notification"); // Final fallback
        // }
      });

      messaging()
        .getInitialNotification()
        .then((remoteMessage) => {
          if (remoteMessage) {
            console.log("Notification caused app to open from quit state:", remoteMessage.notification);
          }
        });

      messaging().onMessage(async (remoteMessage) => {
        console.log("Notification received in foreground:", remoteMessage.data);
      });
    };

    const requestAndroidNotificationPermission = async () => {
      if (Platform.OS === "android") {
        const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log("Notification permission granted.");
          return true;
        } else {
          console.log("Notification permission denied.");
          return false;
        }
      }
      return true;
    };

    const requestUserPermission = async () => {
      if (Platform.OS === "android") {
        const hasPermission = await requestAndroidNotificationPermission();
        if (!hasPermission) return;
      }

      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log("FCM permission granted");
        getFCMToken();
      } else {
        console.log("FCM permission denied");
      }
    };

    useEffect(() => {
      requestUserPermission();
      notificationListener();

      // Handle deep links
      handleInitialDeepLink();
      const linkingSubscription = handleDeepLinkListener();

      return () => {
        // Clean up the linking subscription
        if (linkingSubscription?.remove) {
          linkingSubscription.remove();
        }
      };
    }, []);

    // Handle deep links when user authentication state changes
    useEffect(() => {
      if (user?.userId) {
        // Re-check for initial deep link after user logs in
        handleInitialDeepLink();
      }
    }, [user?.userId]);

    return <RootNavigator />;
  };
  return MessageHandlerComponent;
};
