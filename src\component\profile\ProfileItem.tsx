import {
  StyleSheet,
  Text,
  View,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import React, {ElementType} from 'react';
import {RightArrow} from '../../assets/svg';
import {colors, fonts} from '../../utilities/theme';
interface Props extends TextInputProps {
  onPress?: () => void;
  title?: string;
  icon?: ElementType;
  showProgressbar?: Number;
}

const ProfileItem: React.FC<Props> = ({
  onPress,
  title,
  icon: IconComponent,
  showProgressbar,
}) => {
  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <View style={styles.innerContainer}>
        {IconComponent && <IconComponent />}

        <Text style={styles.titleStyle}>{title}</Text>
        <RightArrow />
      </View>

      {showProgressbar ? (
        <View style={{width: '66%', marginTop: 12, marginLeft: 37}}>
          <View style={styles.inactiveProgress}>
            <View
              style={[
                styles.activeProgress,
                {width: `${showProgressbar}%` as `${number}%`},
              ]}
            />
          </View>
          <Text style={styles.progressTextStyle}>Intermediate</Text>
        </View>
      ) : null}
    </TouchableOpacity>
  );
};

export default ProfileItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 12,
    paddingHorizontal: 18,
    marginHorizontal: 1,
    paddingVertical: 16,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  titleStyle: {
    fontSize: 18,
    fontFamily: fonts.Regular,
    color: colors.primary,
    flex: 1,
    paddingLeft: 12,
  },

  activeProgress: {
    width: '100%',
    height: 10,
    backgroundColor: colors.primary,
    borderRadius: 5,
  },

  inactiveProgress: {
    height: 10,
    backgroundColor: '#e5b5a1',
    borderRadius: 5,
  },

  progressTextStyle: {
    fontSize: 16,
    fontFamily: fonts.Regular,
    color: '#A8A8A8',
    paddingTop: 6,
  },
});
