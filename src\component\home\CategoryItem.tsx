import React from 'react';
import {
  LayoutChangeEvent,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import {colors, fonts} from '../../utilities/theme';
// import {colors, fonts} from '../../screens/utilities/theme';

interface Props {
  title: string;
  selected: boolean;
  onPress: () => void;
}

const CategoryItem: React.FC<Props> = ({title, selected, onPress}) => {
  return (
    <TouchableOpacity
      style={[styles.container, selected && styles.selectedContainer]}
      onPress={onPress}>
      <Text style={[styles.text, selected && styles.selectedText]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default CategoryItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  selectedContainer: {
    backgroundColor: colors.primary,
  },
  text: {
    color: colors.black,
    fontSize: 12,
    fontFamily: fonts.Bold,
  },
  selectedText: {
    color: colors.white,
  },
});
