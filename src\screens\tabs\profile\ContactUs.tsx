import { StyleSheet, View, Text, KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import React, { useState } from "react";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";

import { App<PERSON><PERSON>on, AuthHeader, ProfileInput } from "../../../component";
import { colors, fonts } from "../../../utilities/theme";
import CustomHeader from "../../../component/common/CustomHeader";
import { useFormik } from "formik";
import * as Yup from "yup";
import firestore from "@react-native-firebase/firestore";
import { useUser } from "../../../Hooks/UseContext";
import functions from "@react-native-firebase/functions";

type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList, "ContactUs">;

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  subject: Yup.string().required("Subject is required"),
  message: Yup.string().required("Message is required"),
  email: Yup.string().email("Invalid email").required("Email is required"),
});

const ContactUs: React.FC<Props> = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const { user } = useUser();

  const addMessage = async (name: string, subject: string, message: string) => {
    setLoading(true);
    try {
      const docRef = firestore().collection("ContactMessages").doc();
      await docRef
        .set({
          id: docRef.id,
          name,
          subject,
          message,
          createdAt: firestore.FieldValue.serverTimestamp(),
          userId: user?.userId,
        })
        .then(async () => {
          await functions().httpsCallable("sendContactUsEmailToAdmin")({
            name,
            subject,
            message,
            email: user?.email,
          });
          formik.resetForm(); // Reset form after successful submission
        });
      navigation.navigate("CongratsProfile", {
        title: "Message has been sent Successfully",
      });
    } catch (error) {
      console.error("Error adding document: ", error);
    } finally {
      setLoading(false);
    }
  };
  const formik = useFormik({
    initialValues: {
      name: user?.name || "",
      subject: "",
      message: "",
      email: user?.email,
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { name, subject, message } = values;
      addMessage(name, subject, message);
    },
  });
  return (
    <View style={styles.container}>
      {/* <CustomHeader onPress={() => navigation.goBack()} title="Contact Us" /> */}
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"} // Adjust behavior based on platform
        keyboardVerticalOffset={Platform.OS === "ios" ? 40 : 0} // Offset for iOS to handle status bar height
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }} showsVerticalScrollIndicator={false}>
          <Text style={styles.headerLabel}>
            Please, leave us a message and we’ll get back to you as soon as possible.
          </Text>
          <ProfileInput
            placeholder="<EMAIL>"
            onChangeText={formik.handleChange("email")}
            value={formik.values.email}
            onBlur={formik.handleBlur("email")}
            errorMessage={formik.touched.email && formik.errors.email}
            editable={false}
          />
          <ProfileInput
            placeholder="Your Name"
            onChangeText={formik.handleChange("name")}
            value={formik.values.name}
            onBlur={formik.handleBlur("name")}
            errorMessage={formik.touched.name && formik.errors.name}
          />
          <ProfileInput
            placeholder="Subject"
            onChangeText={formik.handleChange("subject")}
            value={formik.values.subject}
            onBlur={formik.handleBlur("subject")}
            errorMessage={formik.touched.subject && formik.errors.subject}
          />
          <ProfileInput
            inputContainerStyle={{
              height: 150,
              backgroundColor: "#f6f6f6",
              alignItems: "flex-start",
              paddingTop: Platform.OS === "android" ? 0 : 8,
            }}
            inputStyles={{ textAlignVertical: "top" }}
            placeholder="Write Your Message...."
            multiline={true}
            numberOfLines={7}
            onChangeText={formik.handleChange("message")}
            value={formik.values.message}
            onBlur={formik.handleBlur("message")}
            errorMessage={formik.touched.message && formik.errors.message}
          />
          <AppButton title="Send" customStyle={{ marginTop: 65 }} onPress={formik.handleSubmit} isLoading={loading} />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ContactUs;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 32,
    backgroundColor: colors.white,
  },

  headerLabel: {
    fontSize: 15,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
    marginVertical: 40,
  },
});
