import { Platform, StyleSheet, Text, TextInputProps, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native";
import React from "react";
// import {BackArrow} from '../../assets/svgIcons';
import { BackIcon } from "../../assets/svg";
import { appStyles, colors, fonts } from "../../utilities/theme";
interface Props extends TextInputProps {
  title: string;
  showBackArrow?: boolean;
  arrowContainerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  containerStyle?: ViewStyle;
  onPress?: () => void;
}

const CustomHeader: React.FC<Props> = ({
  title,
  showBackArrow = true,
  arrowContainerStyle,
  titleStyle,
  containerStyle,
  onPress,
}) => {
  return (
    <View style={[styles.row, containerStyle]}>
      {showBackArrow && (
        <TouchableOpacity style={[styles.container, arrowContainerStyle]} activeOpacity={0.7} onPress={onPress}>
          <BackIcon />
        </TouchableOpacity>
      )}
      <Text style={[styles.titleStyle, appStyles.h1, titleStyle]} numberOfLines={1}>
        {title}
      </Text>
    </View>
  );
};

export default CustomHeader;

const styles = StyleSheet.create({
  row: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 22,
  },
  titleStyle: {
    fontSize: 22,
    fontFamily: fonts.Bold,
    color: colors.black,
    textAlign: "center",
  },

  container: {
    width: 34,
    height: 34,
    flexDirection: "row",
    position: "absolute",
    left: 0,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,

    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
});
