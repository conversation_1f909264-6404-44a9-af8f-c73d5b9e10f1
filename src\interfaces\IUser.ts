export interface IUserContext {
  user?: IUser;
  setUser: (user: IUser) => void;
}

export interface IUser {
  userId: string;
  name?: string;
  email?: string;
  userType?: 'Admin' | 'User';
  profileImage?: string;
  location?: string;
  address?: string;
  phoneNumber?: string;
  myOrders?: string[];
  originalPhoneNumber?: string; // The copy phone, avoid type error
  myWishList?: string[];
  isOnline?: boolean;
  isTourViewed?: boolean;
  customerId?: string;
}
