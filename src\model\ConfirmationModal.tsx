import React from 'react';
import {Text, StyleSheet, View, ViewStyle, Platform} from 'react-native';
import Modal from 'react-native-modal';
import {AppButton} from '../component';
import {colors, fonts} from '../utilities/theme';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  isDeleteAccount?: boolean;
  onPress?: () => void;
}

const ConfirmationModal: React.FC<Props> = ({
  isVisible,
  onClose,
  isDeleteAccount,
  onPress,
}) => {
  return (
    <Modal
      isVisible={isVisible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onSwipeComplete={onClose}
      backdropOpacity={0.6}
      swipeDirection={'down'}
      onBackdropPress={onClose}
      backdropTransitionOutTiming={0}
      useNativeDriverForBackdrop={true}
      style={{justifyContent: 'flex-end', margin: 0}}>
      <View style={styles.modalContainer}>
        <Text style={styles.title}>
          {isDeleteAccount ? 'Delete Account' : 'Log out'}
        </Text>
        <Text style={styles.subTitle}>
          {isDeleteAccount
            ? 'Are you sure you want to delete your account?'
            : 'Are you sure you want to leave?'}
        </Text>

        <AppButton
          title={isDeleteAccount ? 'Delete' : 'Yes , Log out'}
          customStyle={
            [
              styles.customStyle,
              {
                backgroundColor: isDeleteAccount ? colors.red2 : colors.primary,
              },
            ] as ViewStyle
          }
          onPress={onPress}
        />

        <AppButton
          title="Cancel"
          customStyle={{backgroundColor: colors.white}}
          titleStyle={{color: colors.gray[200]}}
          onPress={onClose}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingBottom: Platform.OS === 'android' ? 12 : 24,
  },
  handle: {
    width: '25%',
    height: 6,
    borderRadius: 2,
    backgroundColor: colors.primary,
    marginTop: 10,
  },
  title: {
    fontSize: 22,
    marginTop: 10,
    fontFamily: fonts.Bold,
    color: colors.primary,
  },
  subTitle: {
    color: colors.blackLight,
    fontSize: 16,
    fontFamily: fonts.Medium,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  innerContainer: {
    flexDirection: 'row',
    paddingLeft: 25,
    marginTop: 8,
  },
  warnText: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.red2,
    paddingLeft: 10,
    paddingRight: 30,
  },
  customStyle: {
    width: '100%',
    marginTop: 15,
  },
});

export default ConfirmationModal;
