import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import { colors, fonts } from "../../../utilities/theme";
import { IProducts } from "../../../interfaces/Products";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { NotificationCheckIcon, NotificationUnCheckIcon } from "../../../assets/svg";
import firestore, { FirebaseFirestoreTypes } from "@react-native-firebase/firestore";
import { IOrderProductDetails, IOrders } from "../../../interfaces/IOrders";
import moment from "moment";
import OrderItemCard from "../../../component/adminCommon/OrderItemCard";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";

type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList, "OrderTracking">;
interface IExtendedProduct extends IProducts, IOrderProductDetails {
  createdAt: FirebaseFirestoreTypes.Timestamp;
}

interface IExtendedOrder extends IOrders {
  products: IExtendedProduct[];
}

const OrderTracking: React.FC<Props> = ({ navigation, route }) => {
  const { orderId } = route.params || {};
  const [order, setOrder] = useState<IExtendedOrder | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchOrderWithProducts = async () => {
      setIsLoading(true);

      try {
        if (!orderId) {
          console.log("No order ID provided");
          setOrder(null);
          setIsLoading(false);
          return;
        }

        // Step 1: Fetch the order
        const orderSnapshot = await firestore().collection("Orders").doc(orderId).get();
        const orderData = orderSnapshot.data();

        if (!orderData || !orderData.productIds || orderData.productIds.length === 0) {
          console.log("No product details found in the order");
          setOrder({ ...(orderData as IOrders), products: [] });
          setIsLoading(false);
          return;
        }

        const productIds: string[] = orderData.productIds.map((p: any) => p.productId);
        const productDetailsFromOrders: IOrderProductDetails[] = orderData.productIds;

        // Step 2: Fetch product data from Products collection
        const productDocs = await firestore()
          .collection("Products")
          .where(firestore.FieldPath.documentId(), "in", productIds)
          .get();

        const productData: IProducts[] = productDocs.docs.map(
          (doc) =>
            ({
              ...doc.data(),
              id: doc.id,
            } as IProducts),
        );

        // Step 3: Merge order product info with product details
        const mergedProducts: IExtendedProduct[] = productDetailsFromOrders.map((orderProduct) => {
          const productDetails = productData.find((product) => product.id === orderProduct.productId);
          return {
            ...orderProduct,
            ...productDetails,
            createdAt: orderProduct.createdAt,
          } as IExtendedProduct;
        });

        // Step 4: Set order object with embedded products array
        setOrder({
          ...orderData,
          products: mergedProducts,
        } as IExtendedOrder);
      } catch (error) {
        console.error("Error fetching order and products:", error);
        setOrder(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderWithProducts();
  }, [orderId]);

  const renderItem = ({ item }: { item: IExtendedProduct }) => {
    return (
      <OrderItemCard
        title={item.title}
        status={item.orderType || ""}
        image={item.image}
        description={item.description}
        price={item.price || 0}
        createdAt={order?.createdAt}
      />
    );
  };
  const ListFooterComponent = () => {
    return (
      <View>
        {order?.orderTracking?.map((item, index) => {
          return (
            <TouchableOpacity key={item.key} activeOpacity={0.5} style={styles.orderContainer} disabled={true}>
              <View style={styles.checkOrderContainer}>
                <View style={{ flexDirection: "row", flex: 1 }}>
                  <View>{item.completed ? <NotificationCheckIcon /> : <NotificationUnCheckIcon />}</View>
                  <Text style={[styles.checkOrderText, { color: item.completed ? colors.primary : "#8F8E8E" }]}>
                    {item.title}
                  </Text>
                </View>
                <Text style={styles.orderLabel}>
                  {item.timestamp ? moment(item.timestamp.seconds * 1000).fromNow() : ""}
                </Text>
              </View>
              <Text style={styles.orderHeading}>{item.description}</Text>
              <Text style={styles.orderLabel}>{item.title}</Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };
  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
      ) : (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 32 }}>
          <Text style={styles.title}>Order Summary</Text>
          <View style={styles.cardContainer}>
            {order?.products?.map((item) => (
              <View key={item.productId}>{renderItem({ item })}</View>
            ))}
          </View>

          <ListFooterComponent />
        </ScrollView>
      )}
      {/* CONFIRMATION MODAL */}
    </View>
  );
};

export default OrderTracking;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  cardContainer: {
    paddingVertical: 20,
    paddingHorizontal: 14,
    backgroundColor: colors.white,
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginHorizontal: 4,
    gap: 16,
    marginTop: 4,
  },
  title: {
    fontSize: 16,
    color: colors.black,
    fontFamily: fonts.Medium,
    marginHorizontal: 4,
    paddingBottom: 8,
  },
  orderContainer: {
    height: 126,
    paddingHorizontal: 17,
    paddingTop: 15,
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginHorizontal: 4,
  },

  checkOrderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },

  checkOrderText: {
    fontSize: 11,
    fontFamily: fonts.Regular,
    marginLeft: 5,
    alignSelf: "center",
    // color: colors.primary,
  },

  orderHeading: {
    fontSize: 16,
    fontFamily: fonts.SemiBold,
    color: colors.black,
    marginVertical: 5,
  },

  orderLabel: {
    fontSize: 10,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
  loader: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
});
