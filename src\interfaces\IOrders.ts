import { FirebaseFirestoreTypes, Timestamp } from "@react-native-firebase/firestore";

export interface IOrderTracking {
  title: string;
  key: string;
  description: string;
  completed: boolean;
  timestamp: FirebaseFirestoreTypes.Timestamp | null;
}
export interface IOrders {
  orderId: string;
  createdAt: Timestamp;
  status: string;
  totalPrice: number;
  userId: string;
  productIds: IOrderProductDetails[];
  orderTitle: string;
  products?: IEnrichedProductDetails[]; // Array to store enriched product details
  paymentIntent?: string;
  paymentIntentId?: string;
  orderType?: "BORROWED" | "PURCHASED"; // "BORROWED" or "PURCHASED"
  orderTracking: IOrderTracking[];
  totalBorrowingSecurity?: number;
  refundId?: string;
}
export interface IOrderProductDetails {
  createdAt: Timestamp;
  orderType: string;
  productId: string;
  quantity: number;
}

export interface IEnrichedProductDetails {
  productId: string;
  status: string;
  title: string;
  subTitle: string;
  image: string;
}
