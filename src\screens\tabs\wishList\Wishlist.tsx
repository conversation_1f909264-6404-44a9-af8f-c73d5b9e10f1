import {
  StyleSheet,
  View,
  FlatList,
  TextInput,
  SafeAreaView,
  ActivityIndicator,
  Text,
  Dimensions,
  Button,
} from "react-native";
import React, { useEffect, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import { AuthHeader, ProductItem, SearchBar } from "../../../component";
import { colors, fonts, paymentSheetStyles } from "../../../utilities/theme";
import { Searchicon } from "../../../assets/svg";
import { IProducts } from "../../../interfaces/Products";
import UseProduct from "../../../Hooks/UseProduct";
import { useUser } from "../../../Hooks/UseContext";
import firestore from "@react-native-firebase/firestore";
import BorrowTermsandCondition from "../../../model/BorrowTermsandCondition";
import BorrowSuccessModal from "../../../model/BorrowSuccessModal";

type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList, "Wishlist">;
const Wishlist: React.FC<Props> = ({ navigation }) => {
  const { user } = useUser();
  const { onPressMyCart, onPressWishList } = UseProduct();
  const [isLoading, setIsLoading] = useState(false);
  const [wishList, setWishList] = useState<IProducts[]>([]);
  const [searchText, setSearchText] = useState("");
  const [filteredProducts, setFilteredProducts] = useState<IProducts[]>([]);
  const [borrowLoading, setBorrowLoading] = useState<{ [key: string]: boolean }>({});
  const [purchaseLoading, setPurchaseLoading] = useState<{
    [key: string]: boolean;
  }>({});
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [borrowItem, setBorrowItem] = useState<IProducts>();

  const handleSearchTextChange = (text: string) => {
    setSearchText(text);
  };
  useEffect(() => {
    const lowercasedFilter = searchText.toLowerCase();
    const filtered = wishList.filter((item) => item.title && item.title.toLowerCase().includes(lowercasedFilter));
    setFilteredProducts(filtered);
  }, [searchText, wishList]);

  useEffect(() => {
    const unsubscribe = fetchWishListProducts();
    return () => unsubscribe();
  }, []);

  const fetchWishListProducts = () => {
    setIsLoading(true);
    const productRef = firestore().collection("Products");
    const queryRef = productRef.where("wishList", "array-contains", user?.userId);

    const unsubscribe = queryRef.onSnapshot(
      (snapshot) => {
        const initialWishList: Array<IProducts> = [];
        snapshot.forEach((result) => {
          initialWishList.push({
            ...result.data(),
            id: result.id,
          } as IProducts);
        });
        setWishList(initialWishList);
        setFilteredProducts(initialWishList);
        setIsLoading(false);
      },
      (error) => {
        console.log("Error while fetching wishlist products:", error);
        setIsLoading(false);
      },
    );
    return unsubscribe;
  };

  const handleFavoritePress = async (item: IProducts) => {
    const updatedWishList = await onPressWishList(item);
    if (updatedWishList) {
      setWishList((prevProducts) => {
        const currentProducts = prevProducts || [];
        return currentProducts.map((product) =>
          product.id === item.id ? { ...product, wishList: updatedWishList } : product,
        );
      });
    }
  };

  const handleBorrowPress = async (item: IProducts) => {
    setBorrowLoading((prevState) => ({
      ...prevState,
      [item.id]: true,
    }));
    const updatedMyCart = await onPressMyCart(item, "BORROWED");
    setBorrowLoading((prevState) => ({
      ...prevState,
      [item.id]: false,
    }));
    if (updatedMyCart) {
      // navigation.navigate('MyCart');
      setSuccessModalVisible(true);
    }
  };

  const handlePurchasePress = async (item: IProducts) => {
    setPurchaseLoading((prevState) => ({
      ...prevState,
      [item.id]: true,
    }));
    const updatedMyCart = await onPressMyCart(item, "PURCHASED");
    setPurchaseLoading((prevState) => ({
      ...prevState,
      [item.id]: false,
    }));
    if (updatedMyCart) {
      navigation.navigate("MyCart");
    }
  };
  return (
    <View style={styles.container}>
      <SafeAreaView />

      <View style={styles.searchInput}>
        <Searchicon style={{ marginRight: 6 }} />
        <TextInput
          style={styles.searchInputText}
          textAlignVertical="center"
          placeholderTextColor={colors.black}
          placeholder="Enter keyword to search..."
          onChangeText={handleSearchTextChange}
        />
      </View>
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get("window").height / 2,
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      ) : filteredProducts.length === 0 ? (
        <Text style={styles.emptyMessage}>Your wishlist is empty. Start adding your favorites now.</Text>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={filteredProducts}
          renderItem={({ item }) => (
            <ProductItem
              title={item.title}
              subTitle={item.subTitle}
              imageProp={item.image}
              price={item.price ?? 0}
              ratingStar={item.ratingStar ?? 0.0}
              ratingReview={item.ratingReview ?? 0}
              purchaseLoading={purchaseLoading[item.id] || false}
              onPressPurchase={() => handlePurchasePress(item)}
              onPressBarrow={() => {
                setModalVisible(true);
                setBorrowItem(item);
              }}
              borrowLoading={borrowLoading[item.id] || false}
              onPress={() =>
                navigation.navigate("ProductDetails", {
                  id: item.id,
                  title: item.title,
                  subTitle: item.subTitle,
                  category: item.category,
                  description: item.description,
                  price: item.price ?? 0,
                  ratingStar: item.ratingStar ?? 0.0,
                  ratingReview: item.ratingReview ?? 0,
                  image: item.image,
                  wishList: item.wishList,
                  borrowingPrice: item.borrowingPrice ?? 0,
                  borrowingSecurity: item.borrowingSecurity ?? 0,
                })
              }
              onPressFavorite={() => handleFavoritePress(item)} // Update this line
              isFavorite={!!(item.wishList && user?.userId && item.wishList.includes(user.userId))}
            />
          )}
          contentContainerStyle={styles.productContainer}
        />
      )}
      <BorrowTermsandCondition
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        onCanclePress={() => setModalVisible(false)}
        onPressBorrow={() => {
          setModalVisible(false);
          borrowItem && handleBorrowPress(borrowItem);
        }}
        borrowingPrice={borrowItem?.borrowingPrice ?? 0}
        borrowingSecurity={borrowItem?.borrowingSecurity ?? 0}
      />
      <BorrowSuccessModal
        borrowSuccessModal={successModalVisible}
        setBorrowSuccessModal={setSuccessModalVisible}
        onPressOk={() => {
          setSuccessModalVisible(false);
          setTimeout(() => {
            navigation.navigate("MyCart");
          }, 300);
        }}
        onBackdropPress={() => {
          setSuccessModalVisible(false);
          setTimeout(() => {
            navigation.navigate("MyCart");
          }, 300);
        }}
        title={borrowItem?.title || ""}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  productContainer: {
    marginTop: 12,
    gap: 16,
    paddingHorizontal: 16,
    paddingBottom: 28,
  },
  searchInput: {
    backgroundColor: colors.gray[300],
    borderRadius: 16,
    flexDirection: "row",
    height: 50,
    paddingHorizontal: 18,
    alignItems: "center",
    marginTop: 18,
    marginHorizontal: 32,
    marginBottom: 8,
  },

  searchInputText: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.black,
  },
  emptyMessage: {
    marginHorizontal: 32,
    textAlign: "center",
    marginTop: Dimensions.get("window").height / 4.4,
    fontSize: 16,
    color: colors.gray[50],
  },
});
export default Wishlist;
