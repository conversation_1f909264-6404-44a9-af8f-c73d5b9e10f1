import { StyleSheet, View, Dimensions, Text, ScrollView, ActivityIndicator, RefreshControl } from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";
import { colors, fonts } from "../../../utilities/theme";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import firestore from "@react-native-firebase/firestore";
import { LineChart } from "react-native-gifted-charts";

type Props = NativeStackScreenProps<AdminBottomTabParamlist & AdminStackParamsList, "Analytics">;

const monthlyLabels = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

const Analytics: React.FC<Props> = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [monthlySales, setMonthlySales] = useState<number[]>(Array(12).fill(0));
  const [monthlyRevenue, setMonthlyRevenue] = useState<number[]>(Array(12).fill(0));
  const [refreshing, setRefreshing] = useState(false);

  const fetchAnalytics = async () => {
    try {
      const snapshot = await firestore().collection("Orders").where("status", "==", "DELIVERED").get();

      const salesCount = Array(12).fill(0);
      const revenueSum = Array(12).fill(0);

      snapshot.forEach((doc) => {
        const order = doc.data();
        const createdAt = order.createdAt?.toDate?.();
        if (!createdAt || !Array.isArray(order.productIds)) return;

        const monthIndex = createdAt.getMonth();

        for (const item of order.productIds) {
          if (item.orderType === "PURCHASED") {
            const quantity = item.quantity ?? 1;
            salesCount[monthIndex] += quantity;
          }
        }

        const totalPrice = order.totalPrice ?? 0;
        const totalBorrowingSecurity = order.totalBorrowingSecurity ?? 0;

        const actualRevenue = totalPrice - totalBorrowingSecurity;
        revenueSum[monthIndex] += actualRevenue;
      });

      setMonthlySales(salesCount);
      setMonthlyRevenue(revenueSum);
    } catch (error) {
      console.log("Error fetching analytics:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const dataLineSales = monthlySales.map((value, index) => ({
    value,
    label: monthlyLabels[index],
  }));

  const dataLineRevenue = monthlyRevenue.map((value, index) => ({
    value,
    label: monthlyLabels[index],
  }));

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchAnalytics();
    setRefreshing(false);
  }, []);

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[colors.primary]} // Android spinner color
          tintColor={colors.primary} // iOS spinner color
        />
      }
      showsVerticalScrollIndicator={false}
      style={styles.container}
    >
      {loading ? (
        <ActivityIndicator size="large" color={colors.primary} style={styles.loadingContainer} />
      ) : (
        <View style={styles.monthlyContainer}>
          <Text style={styles.chartLabel}>Monthly Sales</Text>
          <ScrollView showsVerticalScrollIndicator={false} horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.chartBackground}>
              <LineChart
                areaChart
                data={dataLineSales}
                width={monthlyLabels.length * 60}
                height={300}
                maxValue={Math.max(...monthlySales) + 10}
                noOfSections={5}
                yAxisThickness={0}
                xAxisThickness={0}
                hideDataPoints={false}
                color={colors.secondary}
                thickness={4}
                yAxisTextStyle={{ color: "#999" }}
                startFillColor="#fcf8f5"
                rulesColor="#EEEEEE"
                xAxisLabelTextStyle={{
                  color: "#999",
                  fontSize: 14,
                }}
                rulesType="solid"
                xAxisLabelsVerticalShift={0}
                spacing={60}
                dataPointsColor={colors.primary}
                dataPointsRadius={5}
              />
            </View>
          </ScrollView>

          <Text style={[styles.chartLabel, { marginTop: 20 }]}>Product Revenue</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.chartBackground}>
              <LineChart
                areaChart
                data={dataLineRevenue}
                width={monthlyLabels.length * 60}
                height={300}
                maxValue={Math.max(...monthlyRevenue) + 1000}
                noOfSections={5}
                yAxisThickness={0}
                xAxisThickness={0}
                hideDataPoints={false}
                color={"#6db57e"}
                thickness={4}
                yAxisTextStyle={{ color: "#999" }}
                startFillColor="#eafbea"
                rulesColor="#EEEEEE"
                xAxisLabelTextStyle={{
                  color: "#999",
                  fontSize: 14,
                }}
                rulesType="solid"
                xAxisLabelsVerticalShift={0}
                spacing={60}
                dataPointsColor={"#54a769"}
                dataPointsRadius={5}
              />
            </View>
          </ScrollView>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  chartLabel: {
    fontSize: 18,
    fontFamily: fonts.SemiBold,
    color: "#ce8668",
    marginBottom: 12,
  },
  chartBackground: {
    backgroundColor: colors.white,
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 8,
  },
  monthlyContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingTop: 22,
    paddingBottom: 30,
    paddingHorizontal: 20,
    marginTop: 24,
    marginHorizontal: 16,
    overflow: "hidden",
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  loadingContainer: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default Analytics;
