import { 
  generateDeepLink, 
  parseDeepLink, 
  isValidDeepLink, 
  getDeepLinkUrls,
  shareDeepLink 
} from './deepLinkUtils';

/**
 * Test scenarios for deep linking functionality
 * Run these tests to verify deep linking works correctly
 */

// Test data
const testScenarios = [
  {
    name: 'Product Details',
    screen: 'ProductDetails',
    params: { id: '12345', category: 'electronics' },
    expectedPaths: {
      custom: 'voitto://product/12345?category=electronics',
      web: 'https://voitto.app/product/12345?category=electronics'
    }
  },
  {
    name: 'Chat Details',
    screen: 'ChatDetails',
    params: { recipientId: 'user123' },
    expectedPaths: {
      custom: 'voitto://chat/user123',
      web: 'https://voitto.app/chat/user123'
    }
  },
  {
    name: 'Order Tracking',
    screen: 'OrderTracking',
    params: { orderId: 'order456' },
    expectedPaths: {
      custom: 'voitto://order/order456',
      web: 'https://voitto.app/order/order456'
    }
  },
  {
    name: 'Admin Order Details',
    screen: 'AdminOrderDetails',
    params: { orderId: 'order789' },
    expectedPaths: {
      custom: 'voitto://admin/order/order789',
      web: 'https://voitto.app/admin/order/order789'
    }
  },
  {
    name: 'Notifications',
    screen: 'Notification',
    params: {},
    expectedPaths: {
      custom: 'voitto://notifications',
      web: 'https://voitto.app/notifications'
    }
  },
  {
    name: 'Sign In',
    screen: 'SignIn',
    params: {},
    expectedPaths: {
      custom: 'voitto://signin',
      web: 'https://voitto.app/signin'
    }
  },
  {
    name: 'Home Dashboard',
    screen: 'Home',
    params: {},
    expectedPaths: {
      custom: 'voitto://home/<USER>',
      web: 'https://voitto.app/home/<USER>'
    }
  },
  {
    name: 'Admin Products',
    screen: 'StoreProducts',
    params: {},
    expectedPaths: {
      custom: 'voitto://admin/products',
      web: 'https://voitto.app/admin/products'
    }
  }
];

/**
 * Run all deep link generation tests
 */
export const runGenerationTests = () => {
  console.log('🧪 Running Deep Link Generation Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  testScenarios.forEach((scenario, index) => {
    console.log(`Test ${index + 1}: ${scenario.name}`);
    
    try {
      const customUrl = generateDeepLink(scenario.screen, scenario.params, false);
      const webUrl = generateDeepLink(scenario.screen, scenario.params, true);
      
      console.log(`  Custom URL: ${customUrl}`);
      console.log(`  Web URL: ${webUrl}`);
      
      // Basic validation
      if (customUrl.includes('voitto://') && webUrl.includes('https://voitto.app')) {
        console.log('  ✅ PASSED\n');
        passed++;
      } else {
        console.log('  ❌ FAILED - URLs don\'t match expected format\n');
        failed++;
      }
    } catch (error) {
      console.log(`  ❌ FAILED - Error: ${error.message}\n`);
      failed++;
    }
  });
  
  console.log(`📊 Generation Tests Summary: ${passed} passed, ${failed} failed\n`);
  return { passed, failed };
};

/**
 * Run all deep link parsing tests
 */
export const runParsingTests = () => {
  console.log('🧪 Running Deep Link Parsing Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  testScenarios.forEach((scenario, index) => {
    console.log(`Test ${index + 1}: ${scenario.name} Parsing`);
    
    try {
      const customUrl = generateDeepLink(scenario.screen, scenario.params, false);
      const webUrl = generateDeepLink(scenario.screen, scenario.params, true);
      
      const parsedCustom = parseDeepLink(customUrl);
      const parsedWeb = parseDeepLink(webUrl);
      
      console.log(`  Original Screen: ${scenario.screen}`);
      console.log(`  Parsed Custom: ${parsedCustom?.screen}`);
      console.log(`  Parsed Web: ${parsedWeb?.screen}`);
      
      if (parsedCustom?.screen === scenario.screen && parsedWeb?.screen === scenario.screen) {
        console.log('  ✅ PASSED\n');
        passed++;
      } else {
        console.log('  ❌ FAILED - Screen names don\'t match\n');
        failed++;
      }
    } catch (error) {
      console.log(`  ❌ FAILED - Error: ${error.message}\n`);
      failed++;
    }
  });
  
  console.log(`📊 Parsing Tests Summary: ${passed} passed, ${failed} failed\n`);
  return { passed, failed };
};

/**
 * Run validation tests
 */
export const runValidationTests = () => {
  console.log('🧪 Running Deep Link Validation Tests...\n');
  
  const validUrls = [
    'voitto://product/123',
    'https://voitto.app/chat/user456',
    'voitto://notifications',
    'https://voitto.app/admin/orders'
  ];
  
  const invalidUrls = [
    'https://google.com',
    'invalid-url',
    'http://voitto.app/test', // wrong protocol
    'voitto-wrong://test',
    null,
    undefined,
    ''
  ];
  
  let passed = 0;
  let failed = 0;
  
  console.log('Testing valid URLs:');
  validUrls.forEach((url, index) => {
    const isValid = isValidDeepLink(url);
    console.log(`  ${index + 1}. ${url} -> ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    if (isValid) passed++;
    else failed++;
  });
  
  console.log('\nTesting invalid URLs:');
  invalidUrls.forEach((url, index) => {
    const isValid = isValidDeepLink(url);
    console.log(`  ${index + 1}. ${url || 'null/undefined'} -> ${!isValid ? '✅ Invalid (correct)' : '❌ Valid (incorrect)'}`);
    if (!isValid) passed++;
    else failed++;
  });
  
  console.log(`\n📊 Validation Tests Summary: ${passed} passed, ${failed} failed\n`);
  return { passed, failed };
};

/**
 * Run all tests
 */
export const runAllTests = () => {
  console.log('🚀 Starting Deep Link Tests...\n');
  
  const generationResults = runGenerationTests();
  const parsingResults = runParsingTests();
  const validationResults = runValidationTests();
  
  const totalPassed = generationResults.passed + parsingResults.passed + validationResults.passed;
  const totalFailed = generationResults.failed + parsingResults.failed + validationResults.failed;
  
  console.log('🏁 Final Results:');
  console.log(`   Total Passed: ${totalPassed}`);
  console.log(`   Total Failed: ${totalFailed}`);
  console.log(`   Success Rate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
  
  return {
    passed: totalPassed,
    failed: totalFailed,
    successRate: (totalPassed / (totalPassed + totalFailed)) * 100
  };
};

/**
 * Test deep link sharing functionality
 */
export const testSharing = async () => {
  console.log('🧪 Testing Deep Link Sharing...\n');
  
  try {
    const result = await shareDeepLink(
      'ProductDetails',
      { id: '12345', category: 'electronics' },
      'Check out this amazing product!',
      'I found this great product on Voitto!'
    );
    
    console.log(`Share result: ${result ? 'Success' : 'Failed or Cancelled'}`);
    return result;
  } catch (error) {
    console.log(`Share error: ${error.message}`);
    return false;
  }
};

/**
 * Example usage in a React Native component
 */
export const exampleUsage = () => {
  console.log('📝 Example Usage:\n');
  
  console.log('// Generate deep links');
  console.log('const productLink = generateDeepLink("ProductDetails", { id: "123" });');
  console.log('// Result: voitto://product/123\n');
  
  console.log('// Parse deep links');
  console.log('const parsed = parseDeepLink("voitto://product/123");');
  console.log('// Result: { screen: "ProductDetails", params: { id: "123" } }\n');
  
  console.log('// Validate deep links');
  console.log('const isValid = isValidDeepLink("voitto://product/123");');
  console.log('// Result: true\n');
  
  console.log('// Get both URL formats');
  console.log('const urls = getDeepLinkUrls("ProductDetails", { id: "123" });');
  console.log('// Result: { customScheme: "voitto://product/123", webUrl: "https://voitto.app/product/123" }\n');
  
  console.log('// Share a deep link');
  console.log('await shareDeepLink("ProductDetails", { id: "123" }, "Check this out!");');
  console.log('// Opens native share dialog\n');
};

// Export test scenarios for external use
export { testScenarios };
