import { Text, StyleSheet, View, FlatList, Image, TouchableOpacity, ActivityIndicator, Dimensions } from "react-native";
import React, { useEffect, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import { colors, fonts } from "../../../utilities/theme";
import { IUser } from "../../../interfaces/IUser";
import firestore, { Timestamp } from "@react-native-firebase/firestore";
import { useUser } from "../../../Hooks/UseContext";
import CustomHeader from "../../../component/common/CustomHeader";
import { images } from "../../../assets/images";
import { format, formatDistanceToNow, isToday, isYesterday, parseJSON } from "date-fns";
import UseChat from "../../../Hooks/UseChat";
import ChatListItem from "../../../component/adminCommon/ChatListItem";

type Props = NativeStackScreenProps<AdminBottomTabParamlist & AdminStackParamsList, "UserListChat">;

interface IUserUnreadCount extends IUser {
  unreadMessages: number;
  lastMessage?: {
    text: string;
    createdAt: Timestamp;
  };
}

const UserListChat: React.FC<Props> = ({ navigation }) => {
  const { getConversations, conversations, isLoading } = UseChat();

  useEffect(() => {
    getConversations();
  }, []);

  return (
    <View style={styles.container}>
      {/* <CustomHeader onPress={() => navigation.goBack()} title="Chats" /> */}
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get("window").height / 1.5,
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      ) : (
        <FlatList
          contentContainerStyle={{ marginTop: 20, marginBottom: 24 }}
          showsVerticalScrollIndicator={false}
          data={conversations}
          ListEmptyComponent={() => (
            <Text
              style={{
                fontSize: 16,
                color: colors.black,
                fontFamily: fonts.SemiBold,
                textAlign: "center",
                marginTop: 100,
              }}
            >
              No message found
            </Text>
          )}
          renderItem={({ item }) => {
            return (
              <ChatListItem
                item={item}
                onPress={(recipientUser) => {
                  navigation.navigate("ChatDetails", {
                    recipientUser: recipientUser,
                  });
                }}
              />
            );
          }}
          // keyExtractor={item => item.lastMessage.user.}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: colors.white,
  },

  chatContainer: {
    backgroundColor: colors.white,
    flexDirection: "row",
    borderRadius: 16,
    marginBottom: 12,
    paddingHorizontal: 18,
    marginHorizontal: 1,
    paddingVertical: 10,
    marginTop: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  messageContent: {
    flex: 1,
    justifyContent: "space-evenly",
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },

  timeAgoText: {
    fontSize: 10,
    color: colors.gray[250],
    fontFamily: fonts.Medium,
  },
  nameText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.Medium,
    flex: 1,
  },
  lastMessageText: {
    fontSize: 13,
    color: colors.gray[250],
    fontFamily: fonts.Regular,
    flex: 1,
    marginRight: 20,
  },
  unreadMessagesContainer: {
    width: 20,
    height: 20,
    borderRadius: 10, // Make it circular
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  unreadMessagesText: {
    fontSize: 12,
    color: colors.white,
    fontFamily: fonts.SemiBold,
    textAlign: "center",
  },
  onlineProfileDot: {
    position: "absolute",
    bottom: 4,
    right: -4,
    borderWidth: 2,
    backgroundColor: "#2ECC71",
    borderColor: colors.white,
    borderRadius: 11,
    width: 14,
    height: 14,
  },
});

export default UserListChat;
