import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import React from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AppButton, AuthHeader} from '../../../component';
import {colors, fonts} from '../../../utilities/theme';
import {images} from '../../../assets/images';
import AddCardItems from '../../../component/profile/AddCardItems';
import ToggleBtn from '../../../component/profile/ToggleBtn';
import {Timestamp} from 'firebase/firestore';
import CustomHeader from '../../../component/common/CustomHeader';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'AddCard'
>;
type ProductStatusData = {
  status: string;
  createdAt: Timestamp;
};
const AddCard: React.FC<Props> = ({navigation, route}) => {
  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust behavior based on platform
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0} // Offset for iOS to handle status bar height
      >
        <ScrollView
          // contentContainerStyle={{flexGrow: 1, paddingBottom: 40}}
          showsVerticalScrollIndicator={false}>
          <ImageBackground
            resizeMode="contain"
            source={images.DebitCard}
            style={styles.cardImageContainer}>
            <View style={{marginLeft: 24}}>
              <Text style={styles.cardNumber}>5698 56254 6786 9979</Text>
              <Text style={styles.cardHolderText}>Card Holder</Text>
              <Text style={styles.cardHolderName}>Zain</Text>
            </View>
          </ImageBackground>

          <AddCardItems />

          {/* <ToggleBtn /> */}

          {/* <AppButton title="Add Card" /> */}
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default AddCard;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 32,
    backgroundColor: colors.white,
  },

  cardImageContainer: {
    width: '100%',
    height: Dimensions.get('screen').height / 4,
    justifyContent: 'center',
    marginTop: 16,
  },

  cardNumber: {
    fontSize: 15,
    fontFamily: fonts.Regular,
    color: colors.white,
    letterSpacing: 2,
    paddingTop: 40,
    paddingBottom: 20,
  },

  cardHolderText: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.white,
  },

  cardHolderName: {
    fontSize: 17,
    fontFamily: fonts.Bold,
    color: colors.white,
  },
});
