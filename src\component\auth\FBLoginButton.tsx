import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  View,
} from 'react-native';
import React from 'react';
import {FacebookIconFill} from '../../assets/svg';
import {colors} from '../../utilities/theme';

interface FBLoginButtonProps {
  loading: boolean;
  onPress: () => void;
}

const FBLoginButton: React.FC<FBLoginButtonProps> = ({loading, onPress}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.button}
      disabled={loading}
      onPress={onPress}>
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator
            size="small"
            color={colors.white}
            style={styles.loader}
          />
        </View>
      ) : (
        <>
          <View style={{width: 22}}>
            <FacebookIconFill width={22} height={22} />
          </View>
          <Text style={styles.text}>Continue with Facebook</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

export default FBLoginButton;

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#1877F2',
    height: 42,
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderRadius: 10,
    flexDirection: 'row',
    paddingLeft: 10,
    gap: 16,
    marginTop: 12,
  },
  text: {
    fontFamily: 'HelveticaNeueMedium',
    fontSize: 14,
    fontWeight: Platform.OS === 'android' ? undefined : '500',
    color: colors.white,
  },
  loader: {
    transform: [{translateX: -10}],
  },
  loaderContainer: {
    transform: [{scale: 0.8}],
    position: 'absolute',
    left: '50%',
  },
});
