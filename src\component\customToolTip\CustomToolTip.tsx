import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TooltipProps} from 'rn-tourguide';
import {colors} from '../../utilities/theme';

const CustomTooltip: React.FC<TooltipProps> = ({
  currentStep,
  handleNext,
  handlePrev,
  handleStop,
  isFirstStep,
  isLastStep,
  labels,
}) => {
  return (
    <View style={styles.tooltipContainer}>
      {currentStep?.text ? ( // Safely check for currentStep and its text
        <Text style={styles.tooltipText}>{currentStep.text}</Text>
      ) : (
        <Text style={styles.tooltipText}>No information available</Text> // Fallback text
      )}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={handleStop}>
          <Text style={styles.btnText}>{labels?.skip || 'Skip'}</Text>
        </TouchableOpacity>
        {!isFirstStep && (
          <TouchableOpacity style={styles.button} onPress={handlePrev}>
            <Text style={styles.btnText}>{labels?.previous || 'Previous'}</Text>
          </TouchableOpacity>
        )}
        {isLastStep ? (
          <TouchableOpacity style={styles.button} onPress={handleStop}>
            <Text style={styles.btnText}>{labels?.finish || 'Finish'}</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.button} onPress={handleNext}>
            <Text style={styles.btnText}>{labels?.next || 'Next'}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tooltipContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 4,
  },
  tooltipText: {
    fontSize: 16,
    marginBottom: 8,
    textAlign: 'center',
    color: colors.black,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    padding: 8,
    borderRadius: 4,
  },
  btnText: {
    color: colors.primary,
  },
});

export default CustomTooltip;
