import { Image, StyleSheet, Text, View, TextInputProps, ImageSourcePropType, ViewStyle } from "react-native";
import React from "react";
import AppButton from "../common/AppButton";
import { colors, fonts } from "../../utilities/theme";
import Navigation from "../../navigation/Navigation";
import { AdminEditIcon, AdminRemoveIcon } from "../../assets/svg";

interface Props extends TextInputProps {
  heading?: string;
  title?: string;
  cost?: string;
  date?: string;
  btnText?: string;
  onPressEdit?: () => void;
  imageProp?: string;
  onPressRemove?: () => void;
  btnShow?: boolean;
}
const AdminStoreItem: React.FC<Props> = ({
  heading,
  title,
  cost,
  date,
  btnText,
  imageProp,
  onPressRemove,
  onPressEdit,
  btnShow = false,
}) => (
  <View>
    <View style={styles.itemContainer}>
      <View style={{ flexDirection: "row" }}>
        <View style={{ flex: 1, marginRight: 10 }}>
          <Text style={styles.headingText}>{heading}</Text>
          <Text style={[styles.titleText, { color: colors.gray[50] }]}>{title}</Text>
          <View
            style={{
              flexDirection: "row",
              marginTop: 10,
              marginBottom: 5,
            }}
          >
            <View style={{ flex: 1, marginRight: 10 }}>
              <AppButton
                title="Edit"
                onPress={onPressEdit}
                titleStyle={styles.btnText}
                customStyle={[styles.btnContainer, { backgroundColor: colors.black }] as ViewStyle}
                icon={<AdminEditIcon />}
                removeEdit={true}
              />
            </View>

            <View
              style={{
                flex: 1,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <AppButton
                title="Remove"
                onPress={onPressRemove}
                titleStyle={styles.btnText}
                customStyle={[styles.btnContainer] as ViewStyle}
                icon={<AdminRemoveIcon />}
                removeEdit={true}
              />
            </View>
          </View>
        </View>
        <View style={{ flex: 0.5 }}>
          <Image
            style={{ height: 90, width: "100%", borderRadius: 12, overflow: "hidden" }}
            source={{ uri: imageProp }} // Use the imageProp as the uri
            // resizeMode="contain"
          />
        </View>
      </View>
      <View style={styles.costDateContainer}></View>
    </View>
  </View>
);

export default AdminStoreItem;
const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 2,
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginHorizontal: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  headingText: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  titleText: {
    fontSize: 12,
    fontWeight: "300",
  },

  btnContainer: {
    height: 31,
    paddingHorizontal: 17,
    borderRadius: 15,
  },

  btnText: {
    fontFamily: fonts.Medium,
    fontSize: 9,
  },

  costDateContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },

  costStyle: {
    fontSize: 20,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  dateStyle: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
});
