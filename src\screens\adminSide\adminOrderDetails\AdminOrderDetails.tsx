import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import { colors, fonts } from "../../../utilities/theme";
import { IProducts } from "../../../interfaces/Products";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import { NotificationCheckIcon, NotificationUnCheckIcon, StarIcon } from "../../../assets/svg";
import { useUser } from "../../../Hooks/UseContext";
import firestore, { FirebaseFirestoreTypes, Timestamp } from "@react-native-firebase/firestore";
import { IOrderProductDetails, IOrders, IOrderTracking } from "../../../interfaces/IOrders";
import moment from "moment";
import OrderItemCard from "../../../component/adminCommon/OrderItemCard";
import functions from "@react-native-firebase/functions";
import Toast from "react-native-toast-message";
import ConfirmationModal from "../../../component/ConfirmationModal";
import { AppButton } from "../../../component";
import { showToast } from "../../../helper/toast";
import useNotification from "../../../Hooks/useNotification";

type Props = NativeStackScreenProps<AdminBottomTabParamlist & AdminStackParamsList, "AdminOrderDetails">;

interface IExtendedProduct extends Omit<IProducts, "orderType">, Omit<IOrderProductDetails, "orderType"> {
  orderType: IProducts["orderType"] | IOrderProductDetails["orderType"];
  createdAt: FirebaseFirestoreTypes.Timestamp;
  status: string; // Ensure compatibility with IEnrichedProductDetails
}

interface IExtendedOrder extends IOrders {
  products: IExtendedProduct[];
}

interface IRefundData {
  borrowingSecurity: number;
  orderId: string;
  productId: string;
}

const AdminOrderDetails: React.FC<Props> = ({ navigation, route }) => {
  const { productIds, orderId } = route.params || {};
  const { sendNotification } = useNotification();
  const [order, setOrder] = useState<IExtendedOrder | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [updatedStatusLoadingIndex, setUpdatedStatusLoadingIndex] = useState<number | null>(null);
  const [isRefunding, setRefunding] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [notificationLoading, setNotificationLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchOrderWithProducts = async () => {
      try {
        if (!orderId) {
          console.log("No order ID provided");
          setOrder(null);
          setIsLoading(false);
          return;
        }

        // Step 1: Fetch the order
        const orderSnapshot = await firestore().collection("Orders").doc(orderId).get();
        const orderData = orderSnapshot.data();

        if (!orderData || !orderData.productIds || orderData.productIds.length === 0) {
          console.log("No product details found in the order");
          setOrder({ ...(orderData as IOrders), products: [] });
          setIsLoading(false);
          return;
        }

        const productIds: string[] = orderData.productIds.map((p: any) => p.productId);
        const productDetailsFromOrders: IOrderProductDetails[] = orderData.productIds;

        // Step 2: Fetch product data from Products collection
        const productDocs = await firestore()
          .collection("Products")
          .where(firestore.FieldPath.documentId(), "in", productIds)
          .get();

        const productData: IProducts[] = productDocs.docs.map(
          (doc) =>
            ({
              ...doc.data(),
              id: doc.id,
            } as IProducts),
        );

        // Step 3: Merge order product info with product details
        const mergedProducts: IExtendedProduct[] = productDetailsFromOrders.map((orderProduct) => {
          const productDetails = productData.find((product) => product.id === orderProduct.productId);

          return {
            ...orderProduct,
            ...productDetails,
            createdAt: orderProduct.createdAt,
          } as IExtendedProduct;
        });

        // Step 4: Set order object with embedded products array
        setOrder({
          ...orderData,
          products: mergedProducts,
        } as IExtendedOrder);
      } catch (error) {
        console.error("Error fetching order and products:", error);
        setOrder(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderWithProducts();
  }, [orderId]);

  const refundBorrowedAmount = async (orderId: string, productId: string = "test-id") => {
    try {
      setRefunding(true);

      const response = await functions().httpsCallable("refundBorrowedProductAmount")({
        orderId,
        productId,
      });

      const { amountOfBorrowing, amountOfBorrowingInCents, refund } =
        (response?.data as {
          amountOfBorrowing: number;
          amountOfBorrowingInCents: number;
          refund: any;
        }) || {};

      if (!refund || !refund.id) {
        throw new Error("Refund ID is missing from the response");
      }

      Toast.show({
        type: "success",
        text1: "Refund Successful",
        text2: `Refund ID: ${refund.id}`,
      });
      setIsModalVisible(false);
      await functions().httpsCallable("sendOrderStatusEmailToUser")({
        data: {
          userId: order?.userId,
          statusKey: "refund",
        },
      });
      navigation.goBack();
    } catch (error: any) {
      console.error("Refund failed:", error);

      const message = error?.message || error?.data?.message || "There was an issue processing the refund.";

      Alert.alert("Refund Failed", message);
    } finally {
      setRefunding(false);
    }
  };

  const updateOrderStatus = async (index: number, description: string, orderStatus: string) => {
    setUpdatedStatusLoadingIndex(index);

    const updatedStatus = order?.orderTracking.map((step, i) => {
      if (i < index) return { ...step, completed: true };
      if (i === index)
        return {
          ...step,
          completed: !step.completed,
          timestamp: !step.completed ? firestore.Timestamp.fromDate(new Date()) : null,
        };
      return {
        ...step,
        completed: false,
        timestamp: null,
      };
    });

    // Capital status value based on key
    const statusKeyToEnumMap: Record<string, string> = {
      order_placed: "PLACED",
      order_processing: "PROCESSED",
      order_expected: "DELIVERY",
      order_delivered: "DELIVERED",
    };

    // const completedSteps = (updatedStatus ?? []).filter((s) => s.completed);
    // const newStatusKey = completedSteps.length > 0 ? completedSteps[completedSteps.length - 1].key : "";
    const newOrderStatus = statusKeyToEnumMap[orderStatus] || "PENDING";

    try {
      await firestore().collection("Orders").doc(orderId).update({
        orderTracking: updatedStatus,
        status: newOrderStatus,
      });
      // Update local state too
      setOrder((prev) =>
        prev && Array.isArray(updatedStatus)
          ? { ...prev, orderTracking: updatedStatus as IOrderTracking[], status: orderStatus }
          : prev,
      );
      await functions().httpsCallable("sendOrderStatusEmailToUser")({
        data: {
          userId: order?.userId,
          statusKey: orderStatus,
          subject: description,
        },
      });
    } catch (err) {
      console.error("Failed to update status", err);
    } finally {
      setUpdatedStatusLoadingIndex(null); // Reset loading
    }
  };

  const isOrderFullyCompleted = order?.orderTracking?.every((step) => step.completed === true);

  const sendReminderNotification = async ({ title, body, userId }: { title: string; body: string; userId: string }) => {
    setNotificationLoading(true);
    try {
      // Show local notification
      sendNotification({
        title: title,
        body: body,
        status: "Refund Product",
        userId: userId,
      });
      const result: any = await functions().httpsCallable("sendCustomNotificationToUser")({
        title,
        body,
        userId,
      });

      if (!result.data?.success) {
        console.error("Notification failed:", result.data?.message);
      } else {
        showToast("Notification sent successfully!", "Success", "success");
        console.log("Notification sent:", result.data?.message);
      }
    } catch (error) {
      console.error("Error sending notification:", error);
    } finally {
      setNotificationLoading(false);
    }
  };

  const renderItem = ({ item, index }: { item: IExtendedProduct; index: number }) => {
    return (
      <OrderItemCard
        title={item.title}
        status={item.orderType || ""}
        image={item.image}
        description={item.description}
        price={item.price || 0}
        createdAt={item.createdAt}
        containerStyle={{
          borderBottomWidth: index % 2 !== 0 ? 0 : order?.productIds.length === 1 ? 0 : 1,
          paddingBottom: index % 2 !== 0 ? 0 : order?.productIds.length === 1 ? 0 : 8,
        }}
      />
    );
  };

  const ListFooterComponent = () => {
    return (
      <View>
        {order?.orderTracking?.map((item, index) => {
          const isLoading = updatedStatusLoadingIndex === index;

          const isNextEditable = index === 0 || (order?.orderTracking[index - 1]?.completed && !item.completed);
          return (
            <TouchableOpacity
              key={item.key}
              onPress={() => updateOrderStatus(index, item.description, item.key)}
              activeOpacity={0.5}
              style={styles.orderContainer}
              // disabled={(!isNextEditable && !item.completed) || !!order?.refundId}
              disabled={item.completed || !isNextEditable || !!order?.refundId}
            >
              <View style={styles.checkOrderContainer}>
                <View style={{ flexDirection: "row", flex: 1 }}>
                  <View>{item.completed ? <NotificationCheckIcon /> : <NotificationUnCheckIcon />}</View>
                  <Text style={[styles.checkOrderText, { color: item.completed ? colors.primary : "#8F8E8E" }]}>
                    {item.title}
                  </Text>
                </View>
                <Text style={styles.orderLabel}>
                  {item.timestamp ? moment(item.timestamp.seconds * 1000).fromNow() : ""}
                </Text>
              </View>
              <Text style={styles.orderHeading}>{item.description}</Text>
              <Text style={styles.orderLabel}>{item.title}</Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
      ) : (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 32 }}>
          {isOrderFullyCompleted && (order?.totalBorrowingSecurity ?? 0) > 1 ? (
            <View style={styles.cardContainer}>
              <Text
                style={[
                  styles.title,
                  {
                    paddingBottom: 0,
                    paddingTop: 0,
                    textAlign: "center",
                    fontFamily: fonts.SemiBold,
                  },
                ]}
              >
                Borrowing security{" "}
                <Text style={{ fontFamily: fonts.SemiBold }}>NS${order?.totalBorrowingSecurity}</Text>
              </Text>

              <Text style={[styles.title, { paddingBottom: 0, paddingTop: 0, fontSize: 14, color: "#555555" }]}>
                {order?.refundId
                  ? "This product has already been refunded."
                  : "If he borrowed product has been returned, you may proceed with the refund."}
              </Text>
              <AppButton
                title={order?.refundId ? "Refunded" : "Refund Now"}
                customStyle={{ backgroundColor: order?.refundId ? "#9A58D3" : "#FEC82D" }}
                disabled={!!order?.refundId}
                onPress={() => setIsModalVisible(true)}
              />
              {order?.refundId ? null : (
                <AppButton
                  title={"Send Reminder"}
                  titleStyle={{ color: "#FEC82D" }}
                  customStyle={{ borderColor: "#FEC82D", borderWidth: 2, backgroundColor: "transparent" }}
                  disabled={!!order?.refundId || notificationLoading}
                  isLoading={notificationLoading}
                  color={"#FEC82D"}
                  onPress={() =>
                    sendReminderNotification({
                      title: "Reminder: Return Borrowed Product",
                      body: "You have a borrowed product that needs to be returned. Please return it as soon as possible.",
                      userId: order?.userId || "",
                    })
                  }
                />
              )}
            </View>
          ) : null}
          <Text style={styles.title}>Order Summary</Text>
          <View style={styles.cardContainer}>
            {order?.products?.map((item, index) => (
              <View key={item.productId}>{renderItem({ item, index })}</View>
            ))}
          </View>

          <ListFooterComponent />
        </ScrollView>
      )}
      {/* CONFIRMATION MODAL */}
      <ConfirmationModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        title="Refund Payment?"
        subtitle={`Are you sure that product is returned, and you want to refund $NZ${order?.totalBorrowingSecurity} ?`}
        cancelLabel="Cancel"
        confirmLabel="Yes, refund"
        isConfirmLoading={isRefunding}
        onPress={() => order?.orderId && refundBorrowedAmount(order?.orderId)}
      />
    </View>
  );
};

export default AdminOrderDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
    paddingTop: 20,
  },
  cardContainer: {
    paddingVertical: 20,
    paddingHorizontal: 14,
    backgroundColor: colors.white,
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginHorizontal: 4,
    gap: 12,
    marginTop: 4,
  },
  title: {
    fontSize: 16,
    color: colors.black,
    fontFamily: fonts.Medium,
    marginHorizontal: 4,
    paddingBottom: 8,
    paddingTop: 16,
  },
  orderContainer: {
    paddingHorizontal: 17,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginHorizontal: 4,
  },

  checkOrderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },

  checkOrderText: {
    fontSize: 11,
    fontFamily: fonts.Regular,
    marginLeft: 5,
    alignSelf: "center",
    // color: colors.primary,
  },

  orderHeading: {
    fontSize: 16,
    fontFamily: fonts.SemiBold,
    color: colors.black,
    marginVertical: 5,
  },

  orderLabel: {
    fontSize: 10,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
  loader: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
});
