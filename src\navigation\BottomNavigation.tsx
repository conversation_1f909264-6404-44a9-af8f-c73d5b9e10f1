import * as React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { images } from "../assets/images";
import { useNavigation } from "@react-navigation/native";
import { appStyles, colors, fonts } from "../utilities/theme";
import { Profile, Search, Store, Wishlist } from "../screens/tabs";
import { BackIcon } from "../assets/svg";
import firestore from "@react-native-firebase/firestore";
import { useUser } from "../Hooks/UseContext";

export type BottomTabParamlist = {
  Search: undefined;
  Wishlist: undefined;
  Store: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<BottomTabParamlist>();

function BottomTabs() {
  const { user } = useUser();
  const navigation = useNavigation();
  const [totalCartItems, setTotalCartItems] = React.useState(0);

  const fetchCartItems = () => {
    const userRef = firestore().collection("Users").doc(user?.userId);
    const unsubscribe = userRef.onSnapshot(
      (docSnapshot) => {
        if (docSnapshot.exists) {
          const userData = docSnapshot.data();
          const myCart = userData?.myCart || {};
          const totalItems = Object.keys(myCart).length;
          setTotalCartItems(totalItems);
        } else {
          setTotalCartItems(0);
        }
      },
      (error) => {
        console.log("Error while fetching cart items:", error);
      },
    );

    return unsubscribe;
  };
  React.useEffect(() => {
    const unsubscribe = fetchCartItems();
    return () => unsubscribe();
  }, []);
  return (
    <Tab.Navigator
      initialRouteName="Search"
      screenOptions={({ route }) => ({
        tabBarHideOnKeyboard: true,
        tabBarActiveTintColor: colors.primary,
        tabBarStyle: styles.tabBarStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
        tabBarItemStyle: styles.tabBarItemStyle,
        headerTitleStyle: appStyles.headerTitleStyle,
        headerTitleAlign: "center",
        headerShown: true,
        tabBarShowLabel: false,
        headerShadowVisible: false,
        // headerLeft: () => {
        //   return (
        //     <TouchableOpacity
        //       style={[appStyles.iconContainer, { marginLeft: 32 }]}
        //       activeOpacity={0.7}
        //       onPress={() => navigation.goBack()}
        //     >
        //       <BackIcon />
        //     </TouchableOpacity>
        //   );
        // },
        tabBarIcon: ({ color, focused }) => {
          let source;
          switch (route.name) {
            case "Search":
              source = images.search;
              color = focused ? colors.primary : colors.black;
              break;
            case "Wishlist":
              source = images.wishlist;
              color = focused ? colors.primary : colors.black;
              break;
            case "Store":
              source = images.store;
              color = focused ? colors.primary : colors.black;
              break;
            case "Profile":
              source = images.profile;
              color = focused ? colors.primary : colors.black;
              break;
          }
          return (
            <View style={styles.iconContainer}>
              <Image resizeMode="contain" style={[styles.icon, { tintColor: color }]} source={source} />
              {focused ? <View style={styles.underline} /> : <View style={styles.underline2} />}
            </View>
          );
        },
      })}
    >
      <Tab.Screen name="Search" component={Search} />
      <Tab.Screen name="Wishlist" component={Wishlist} options={{ headerTitle: "Wishlist" }} />
      <Tab.Screen
        name="Store"
        component={Store}
        options={{
          headerTitle: "My Cart",
          tabBarBadge: totalCartItems,
          tabBarBadgeStyle: { position: "absolute", left: 3, right: 0, top: 0 },
        }}
      />
      <Tab.Screen name="Profile" component={Profile} options={{ headerTitle: "My Profile" }} />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  icon: {
    width: 22,
    height: 22,
  },
  iconContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  underline: {
    width: 12,
    height: 6,
    backgroundColor: colors.primary,
    marginTop: 4,
    borderRadius: 100,
  },
  underline2: {
    width: 12,
    height: 6,
    backgroundColor: colors.white,
    marginTop: 4,
    borderRadius: 100,
  },
  tabBarStyle: {
    height: 70,
    paddingTop: 15,
    paddingBottom: 10,
    backgroundColor: colors.bgcolor,
  },
  tabBarItemStyle: {
    height: 40,
  },
  headerTitleStyle: {
    color: colors.black,
    fontSize: 18,
    fontFamily: fonts.Bold,
    lineHeight: 20,
  },
  tabBarLabelStyle: {
    justifyContent: "center",
  },

  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
});

export default BottomTabs;
