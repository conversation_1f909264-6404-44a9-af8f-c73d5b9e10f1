import React, { useState } from "react";
import { StyleSheet, View, Text } from "react-native";
import { AirbnbRating } from "react-native-ratings";
import { appStyles, colors, fonts } from "../../utilities/theme";
import FormInput from "../common/FormInput";
import AppButton from "../common/AppButton";
import { IProducts } from "../../interfaces/Products";
import firestore from "@react-native-firebase/firestore";
import { useUser } from "../../Hooks/UseContext";
import { IProductReview } from "../../interfaces/Products";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Timestamp } from "@react-native-firebase/firestore";
import { showToast } from "../../helper/toast";
import Stars from "react-native-stars";
import { InactiveStarIcon, StarIcon } from "../../assets/svg";
interface Props {
  productItem: IProducts;
  onAddNewRating: (ratingReview: number, ratingStar: number) => void;
}

const Rating: React.FC<Props> = ({ productItem, onAddNewRating }) => {
  const { user } = useUser();
  const [ratingStars, setRatingStars] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const ratingCompleted = (rating: number) => {
    setRatingStars(rating);
  };

  const validationSchema = Yup.object().shape({
    reviewText: Yup.string().required("Review text is required").min(5, "Review must be at least 5 characters long"),
  });
  const formik = useFormik({
    initialValues: {
      reviewText: "",
    },

    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { reviewText } = values;
      addRatingReview(reviewText, ratingStars);
    },
  });

  const addRatingReview = async (reviewText: string, rating: number) => {
    setIsLoading(true);
    try {
      const reviewsQuery = await firestore()
        .collection("ProductReviews")
        .where("productId", "==", productItem.id)
        .where("userId", "==", user?.userId)
        .get();
      if (!reviewsQuery.empty) {
        showToast("You have already reviewed for this product");
        setIsLoading(false);
        return;
      }
      const reviewRef = firestore().collection("ProductReviews").doc();
      const reviewData: IProductReview = {
        reviewId: reviewRef.id,
        productId: productItem.id,
        userId: user?.userId || "",
        userName: user?.name || "",
        userDisplayImage: user?.profileImage || "",
        ratingStars: rating,
        reviewText: reviewText,
        createdAt: firestore.FieldValue.serverTimestamp() as Timestamp,
      };
      await reviewRef.set(reviewData);

      const productRef = firestore().collection("Products").doc(productItem.id);
      const productSnapshot = await productRef.get();
      if (productSnapshot.exists) {
        const productData = productSnapshot.data() as IProducts;
        const currentReviewCount = productData.ratingReview || 0;
        const currentRatingStar = productData.ratingStar || 0;

        const currentRatingTotal = currentRatingStar * currentReviewCount;
        const newRatingReviewCount = currentReviewCount + 1;
        const newRatingTotal = currentRatingTotal + reviewData.ratingStars;
        const newAverageRating = newRatingTotal / newRatingReviewCount;

        await productRef.update({
          ratingReview: newRatingReviewCount,
          ratingStar: newAverageRating,
          reviews: firestore.FieldValue.arrayUnion(reviewRef.id),
        });
        onAddNewRating(newRatingReviewCount, newAverageRating);
      }
      formik.setFieldValue("reviewText", "");
      formik.setFieldTouched("reviewText", false, false);
      showToast("Review added successfully", "success", "success");
    } catch (error) {
      console.error("Error adding review:", error);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Text style={appStyles.h4}>Leave a review</Text>
        <Stars
          count={5}
          default={ratingStars}
          half={false}
          spacing={4}
          update={ratingCompleted}
          fullStar={<StarIcon />}
          emptyStar={<InactiveStarIcon />}
        />
      </View>
      <FormInput
        placeholder="Start writing here"
        inputContainerStyle={{ backgroundColor: "#f6f6f6" }}
        value={formik.values.reviewText}
        onChangeText={formik.handleChange("reviewText")}
        onBlur={formik.handleBlur("reviewText")}
        errorMessage={formik.touched.reviewText && formik.errors.reviewText}
      />
      <AppButton title="Publish" customStyle={{ marginTop: 20 }} onPress={formik.handleSubmit} isLoading={isLoading} />
    </View>
  );
};

export default Rating;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    marginBottom: 40,
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginHorizontal: 27,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  errorText: {
    color: colors.red2,
    marginTop: 5,
    fontSize: 12,
    fontFamily: fonts.Medium,
  },
});
