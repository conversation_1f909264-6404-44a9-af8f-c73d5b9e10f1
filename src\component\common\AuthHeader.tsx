import {
  StyleSheet,
  View,
  TextInputProps,
  TouchableOpacity,
  Image,
  Text,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  Platform,
} from 'react-native';
import React, {ElementType} from 'react';
import {colors, fonts} from '../../utilities/theme';
import {
  BackIcon,
  LeftIcon,
  MyCart,
  NotificationIcon,
  ProfileEditRadius,
} from '../../assets/svg';
import {useUser} from '../../Hooks/UseContext';
interface Props extends TextInputProps {
  onPress?: () => void;
  onPressNotification?: () => void;
  onPressImage?: () => void;
  title?: string;
  icon?: ElementType;
  backArrow?: boolean;
  arrowContainerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  showTitle?: boolean;
  onPressMyCart?: () => void;
}

const AuthHeader: React.FC<Props> = ({
  onPress,
  onPressNotification,
  onPressImage,
  title,
  backArrow = false,
  arrowContainerStyle,
  titleStyle,
  showTitle = false,
  onPressMyCart,
}) => {
  const {user} = useUser();

  return (
    <>
      {backArrow ? (
        showTitle ? (
          <Text style={[styles.titleStyle, {marginTop: 22}, titleStyle]}>
            {title}
          </Text>
        ) : (
          <View style={[styles.arrowContainer, arrowContainerStyle]}>
            <TouchableOpacity
              style={styles.container}
              activeOpacity={0.7}
              onPress={onPress}>
              <BackIcon />
            </TouchableOpacity>
            <Text style={[styles.titleStyle, titleStyle]}>{title}</Text>
            <View style={{width: 34}} />
          </View>
        )
      ) : (
        <View style={styles.headercontainer}>
          <View style={styles.leftIconContainer}>
            <LeftIcon />
          </View>
          <TouchableOpacity
            style={styles.notificationContainer}
            onPress={onPressNotification}>
            <NotificationIcon />
          </TouchableOpacity>
        </View>
      )}
    </>
  );
};

export default AuthHeader;
const styles = StyleSheet.create({
  arrowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Platform.OS === 'android' ? 16 : 8,
    justifyContent: 'space-between',
  },
  container: {
    width: 34,
    height: 34,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  titleStyle: {
    fontSize: 22,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  headercontainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    marginHorizontal: 32,
  },
  leftIconContainer: {
    paddingRight: 20,
  },

  notificationContainer: {
    backgroundColor: colors.white,
    width: 48,
    height: 48,
    borderRadius: 18,
    alignItems: 'center',
    paddingVertical: 13,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 18,
  },
  imgStyle: {width: 50, height: 50, borderRadius: 18},
});
