import { Linking, Share } from 'react-native';

// Base URLs for deep linking
const DEEP_LINK_SCHEME = 'voitto://';
const WEB_BASE_URL = 'https://voitto.app';

/**
 * Generate a deep link URL for a specific screen and parameters
 * @param {string} screen - The screen name
 * @param {object} params - Parameters to pass to the screen
 * @param {boolean} useWebUrl - Whether to use web URL instead of custom scheme
 * @returns {string} The generated deep link URL
 */
export const generateDeepLink = (screen, params = {}, useWebUrl = false) => {
  const baseUrl = useWebUrl ? WEB_BASE_URL : DEEP_LINK_SCHEME;
  
  let path = '';
  const queryParams = new URLSearchParams();
  
  // Add non-path parameters to query string
  Object.keys(params).forEach(key => {
    if (params[key] !== undefined && params[key] !== null) {
      queryParams.append(key, params[key].toString());
    }
  });
  
  const queryString = queryParams.toString();
  
  switch (screen) {
    case 'ProductDetails':
      path = `/product/${params.id || ''}`;
      break;
    case 'ChatDetails':
      path = `/chat/${params.recipientId || ''}`;
      break;
    case 'OrderTracking':
      path = `/order/${params.orderId || ''}`;
      break;
    case 'AdminOrderDetails':
      path = `/admin/order/${params.orderId || ''}`;
      break;
    case 'Notification':
      path = '/notifications';
      break;
    case 'SignIn':
      path = '/signin';
      break;
    case 'Register':
      path = '/register';
      break;
    case 'EditProfile':
      path = '/edit-profile';
      break;
    case 'ContactUs':
      path = '/contact-us';
      break;
    case 'Orders':
      path = '/orders';
      break;
    case 'Options':
      path = '/options';
      break;
    case 'AddCard':
      path = '/add-card';
      break;
    case 'Home':
      path = '/home/<USER>';
      break;
    case 'Search':
      path = '/home/<USER>';
      break;
    case 'Profile':
      path = '/home/<USER>';
      break;
    case 'Cart':
      path = '/home/<USER>';
      break;
    case 'AdminProfile':
      path = '/admin/profile';
      break;
    case 'StoreProducts':
      path = '/admin/products';
      break;
    case 'StoreCategories':
      path = '/admin/categories';
      break;
    case 'Analytics':
      path = '/admin/analytics';
      break;
    case 'Management':
      path = '/admin/orders';
      break;
    case 'AddProducts':
      path = '/admin/add-product';
      break;
    case 'AddCategories':
      path = '/admin/add-category';
      break;
    case 'UserListChat':
      path = '/admin/chats';
      break;
    default:
      path = `/${screen.toLowerCase()}`;
  }
  
  const fullUrl = `${baseUrl}${path}${queryString ? `?${queryString}` : ''}`;
  return fullUrl;
};

/**
 * Parse a deep link URL and extract screen and parameters
 * @param {string} url - The deep link URL to parse
 * @returns {object} Object containing screen name and parameters
 */
export const parseDeepLink = (url) => {
  if (!url) return null;
  
  try {
    const urlObj = new URL(url);
    const path = urlObj.pathname;
    const params = Object.fromEntries(urlObj.searchParams);
    
    let screen = null;
    let routeParams = { ...params };
    
    // Parse different path patterns
    if (path.includes('/product/')) {
      screen = 'ProductDetails';
      const productId = path.split('/product/')[1];
      if (productId) routeParams.id = productId;
    } else if (path.includes('/chat/')) {
      screen = 'ChatDetails';
      const recipientId = path.split('/chat/')[1];
      if (recipientId) routeParams.recipientId = recipientId;
    } else if (path.includes('/admin/order/')) {
      screen = 'AdminOrderDetails';
      const orderId = path.split('/admin/order/')[1];
      if (orderId) routeParams.orderId = orderId;
    } else if (path.includes('/order/')) {
      screen = 'OrderTracking';
      const orderId = path.split('/order/')[1];
      if (orderId) routeParams.orderId = orderId;
    } else if (path === '/notifications') {
      screen = 'Notification';
    } else if (path === '/signin') {
      screen = 'SignIn';
    } else if (path === '/register') {
      screen = 'Register';
    } else if (path === '/edit-profile') {
      screen = 'EditProfile';
    } else if (path === '/contact-us') {
      screen = 'ContactUs';
    } else if (path === '/orders') {
      screen = 'Orders';
    } else if (path === '/options') {
      screen = 'Options';
    } else if (path === '/add-card') {
      screen = 'AddCard';
    } else if (path === '/home/<USER>') {
      screen = 'Home';
    } else if (path === '/home/<USER>') {
      screen = 'Search';
    } else if (path === '/home/<USER>') {
      screen = 'Profile';
    } else if (path === '/home/<USER>') {
      screen = 'Cart';
    } else if (path === '/admin/profile') {
      screen = 'AdminProfile';
    } else if (path === '/admin/products') {
      screen = 'StoreProducts';
    } else if (path === '/admin/categories') {
      screen = 'StoreCategories';
    } else if (path === '/admin/analytics') {
      screen = 'Analytics';
    } else if (path === '/admin/orders') {
      screen = 'Management';
    } else if (path === '/admin/add-product') {
      screen = 'AddProducts';
    } else if (path === '/admin/add-category') {
      screen = 'AddCategories';
    } else if (path === '/admin/chats') {
      screen = 'UserListChat';
    }
    
    return {
      screen,
      params: routeParams,
      originalUrl: url,
      path
    };
  } catch (error) {
    console.log('Error parsing deep link:', error);
    return null;
  }
};

/**
 * Open a deep link URL
 * @param {string} url - The deep link URL to open
 * @returns {Promise<boolean>} Whether the URL was successfully opened
 */
export const openDeepLink = async (url) => {
  try {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
      return true;
    } else {
      console.log('Cannot open URL:', url);
      return false;
    }
  } catch (error) {
    console.log('Error opening deep link:', error);
    return false;
  }
};

/**
 * Share a deep link URL
 * @param {string} screen - The screen name
 * @param {object} params - Parameters to pass to the screen
 * @param {string} title - Title for the share dialog
 * @param {string} message - Message for the share dialog
 * @returns {Promise<boolean>} Whether the share was successful
 */
export const shareDeepLink = async (screen, params = {}, title = 'Check this out!', message = '') => {
  try {
    const webUrl = generateDeepLink(screen, params, true);
    const customUrl = generateDeepLink(screen, params, false);
    
    const shareMessage = message || `${title}\n\nOpen in app: ${customUrl}\nOr visit: ${webUrl}`;
    
    const result = await Share.share({
      title,
      message: shareMessage,
      url: webUrl, // iOS will use this
    });
    
    return result.action === Share.sharedAction;
  } catch (error) {
    console.log('Error sharing deep link:', error);
    return false;
  }
};

/**
 * Validate if a URL is a valid deep link for this app
 * @param {string} url - The URL to validate
 * @returns {boolean} Whether the URL is a valid deep link
 */
export const isValidDeepLink = (url) => {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    const isCustomScheme = url.startsWith(DEEP_LINK_SCHEME);
    const isWebUrl = urlObj.hostname === 'voitto.app';
    
    return isCustomScheme || isWebUrl;
  } catch (error) {
    return false;
  }
};

/**
 * Get the current deep link URL for a screen
 * @param {string} screen - The screen name
 * @param {object} params - Parameters for the screen
 * @returns {object} Object containing both custom scheme and web URLs
 */
export const getDeepLinkUrls = (screen, params = {}) => {
  return {
    customScheme: generateDeepLink(screen, params, false),
    webUrl: generateDeepLink(screen, params, true)
  };
};
