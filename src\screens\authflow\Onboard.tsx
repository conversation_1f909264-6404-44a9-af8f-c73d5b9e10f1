// import React, {useState, useRef, useEffect, useCallback} from 'react';
// import {
//   ScrollView,
//   StyleSheet,
//   Animated,
//   View,
//   Dimensions,
//   Image,
//   Text,
//   TouchableOpacity,
//   SafeAreaView,
//   ImageBackground,
// } from 'react-native';
// import {SLIDES} from '../../constants';
// import type {NativeStackScreenProps} from '@react-navigation/native-stack';
// import {AuthStackParamList} from '../../navigation/AuthNavigation';
// import {colors, fonts} from '../../utilities/theme';
// import AppButton from '../../component/common/AppButton';
// import AsyncStorage from '@react-native-async-storage/async-storage';
// import {useUser} from '../../Hooks/UseContext';
// import firestore from '@react-native-firebase/firestore';
// import {OnboardVideoIcon} from '../../assets/svg';
// import OnboardingVideoModal from '../../model/OnboardingVideoModal';
// type Props = NativeStackScreenProps<AuthStackParamList, 'Onboard'>;

// const sizes = {
//   screenWidth: Dimensions.get('screen').width,
//   screenHeight: Dimensions.get('screen').height,
// };

// const Onboard: React.FC<Props> = ({navigation}) => {
//   const {setFirstLaunch} = useUser();
//   const [selectedIndex, setSelectedIndex] = useState(0);
//   const scrollViewRef = useRef<ScrollView>(null);
//   const scrollX = new Animated.Value(0);
//   const [videoUrl, setVideoUrl] = useState<string | null>(null);
//   const [isPlaying, setIsPlaying] = useState(false);
//   const {width, height} = Dimensions.get('window');

//   useEffect(() => {
//     const fetchVideoUrl = async () => {
//       try {
//         const snapshot = await firestore().collection('Videos').limit(1).get();
//         if (!snapshot.empty) {
//           const videoDoc = snapshot.docs[0];
//           const videoUrl = videoDoc.data()?.url;
//           if (videoUrl) {
//             setVideoUrl(videoUrl);
//           } else {
//             console.error('No URL field found in the document');
//           }
//         } else {
//           console.error('No documents found in the Videos collection');
//         }
//       } catch (error) {
//         console.error('Error fetching video URL:', error);
//       }
//     };

//     fetchVideoUrl();
//   }, []);

//   const handleStartedbtn = async () => {
//     try {
//       await AsyncStorage.setItem('appLaunched', 'false'); // Store that onboarding is completed
//       setFirstLaunch(false); // Update state in UserContext
//       navigation.navigate('SignIn'); // Navigate to the sign-in screen
//     } catch (error) {
//       console.error('Error storing onboarding state:', error);
//     }
//   };

//   return (
//     <SafeAreaView style={{flex: 1, backgroundColor: colors.white}}>
//       <ScrollView
//         ref={scrollViewRef}
//         horizontal
//         pagingEnabled
//         showsHorizontalScrollIndicator={false}
//         onMomentumScrollEnd={event => {
//           const selectedIndex = Math.floor(
//             event.nativeEvent.contentOffset.x / Math.floor(sizes.screenWidth),
//           );
//           setSelectedIndex(selectedIndex);
//         }}
//         onScroll={Animated.event(
//           [{nativeEvent: {contentOffset: {x: scrollX}}}],
//           {
//             useNativeDriver: false,
//           },
//         )}
//         scrollEventThrottle={16}>
//         {SLIDES.map((item, index) => {
//           const inputRange = [
//             (index - 1) * sizes.screenWidth,
//             index * sizes.screenWidth,
//             (index + 1) * sizes.screenWidth,
//           ];
//           const scale = scrollX.interpolate({
//             inputRange,
//             outputRange: [0.3, 1, 0.3],
//             extrapolate: 'extend',
//           });
//           const opacity = scrollX.interpolate({
//             inputRange,
//             outputRange: [0.3, 1, 0.3],
//             extrapolate: 'extend',
//           });
//           const rotateY = scrollX.interpolate({
//             inputRange,
//             outputRange: ['-30deg', '0deg', '30deg'],
//             extrapolate: 'extend',
//           });

//           return (
//             <View
//               key={index}
//               style={[styles.container, {width: sizes.screenWidth}]}>
//               <Animated.View
//                 style={{
//                   transform: [{scale}, {rotateY}],
//                   opacity,
//                 }}>
//                 <View
//                   style={{
//                     marginTop: Dimensions.get('window').height * 0.02,
//                   }}
//                 />

//                 {index === 3 ? (
//                   <>
//                     <ImageBackground
//                       style={{
//                         height: Dimensions.get('window').height * 0.5,
//                         width: '100%',
//                       }}
//                       source={item.image}
//                       resizeMode="contain">
//                       <TouchableOpacity
//                         onPress={() => setIsPlaying(true)}
//                         style={{marginLeft: 10, marginTop: 10}}>
//                         <OnboardVideoIcon />
//                       </TouchableOpacity>
//                     </ImageBackground>
//                   </>
//                 ) : (
//                   <Image
//                     style={{height: 390, width: '100%', borderRadius: 16}}
//                     source={item.image}
//                     // resizeMode="contain"
//                     resizeMode="cover"
//                   />
//                 )}
//                 <Text style={styles.titleStyle}>{item.title}</Text>
//                 <Text style={styles.subTitle}>{item.paragraph}</Text>

//                 {index === 3 ? (
//                   <AppButton
//                     title="Get Started"
//                     customStyle={styles.btnContainer}
//                     onPress={handleStartedbtn}
//                   />
//                 ) : null}
//               </Animated.View>
//             </View>
//           );
//         })}
//       </ScrollView>
//       <OnboardingVideoModal
//         isVisible={isPlaying}
//         onClose={() => setIsPlaying(false)}
//         videoUrl={videoUrl}
//       />
//       {selectedIndex === 3 ? null : (
//         <View style={styles.dotContainer}>
//           {SLIDES.map((_, index) => {
//             const dotColor = scrollX.interpolate({
//               inputRange: [
//                 (index - 1) * width,
//                 index * width,
//                 (index + 1) * width,
//               ],
//               outputRange: ['#BB542720', colors.primary, '#BB542720'],
//               extrapolate: 'clamp',
//             });

//             const dotWidth = scrollX.interpolate({
//               inputRange: [
//                 (index - 1) * width,
//                 index * width,
//                 (index + 1) * width,
//               ],
//               outputRange: [28, 70, 28],
//               extrapolate: 'clamp',
//             });
//             const dotHeight = scrollX.interpolate({
//               inputRange: [
//                 (index - 1) * width,
//                 index * width,
//                 (index + 1) * width,
//               ],
//               outputRange: [10, 10, 10],
//               extrapolate: 'clamp',
//             });

//             return (
//               <Animated.View
//                 key={index}
//                 style={[
//                   styles.dot,
//                   {
//                     backgroundColor: index === 3 ? undefined : dotColor,
//                     width: dotWidth,
//                     height: dotHeight,
//                   },
//                 ]}
//               />
//             );
//           })}
//         </View>
//       )}
//     </SafeAreaView>
//   );
// };

// export default Onboard;

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     paddingHorizontal: 32,
//     backgroundColor: colors.white,
//   },

//   titleStyle: {
//     marginTop: Dimensions.get('window').height * 0.03,
//     fontSize: Dimensions.get('window').width * 0.08,
//     fontFamily: fonts.SemiBold,
//     textAlign: 'center',
//     color: colors.primary,
//     paddingHorizontal: 28,
//   },
//   subTitle: {
//     fontSize: 17,
//     fontFamily: fonts.Regular,
//     color: '#464646',
//     textAlign: 'center',
//   },

//   dotContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//     alignItems: 'center',
//     position: 'absolute',
//     bottom: Dimensions.get('screen').height / 7.6,
//     width: '100%',
//     gap: 6,
//   },
//   dot: {
//     height: 13,
//     borderRadius: 16,
//   },
//   activeDot: {
//     width: 49,
//     height: 13,
//     backgroundColor: colors.primary,
//     borderRadius: 16,
//   },

//   btnContainer: {
//     marginTop: sizes.screenHeight / 12,
//   },
// });
import React, {useRef, useState} from 'react';
import {
  View,
  SafeAreaView,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
  Platform,
} from 'react-native';
import AppIntroSlider from 'react-native-app-intro-slider';
import {images} from '../../assets/images';
import {colors, fonts} from '../../utilities/theme';
import OnboardingVideoModal from '../../model/OnboardingVideoModal';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useUser} from '../../Hooks/UseContext';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {BackArrowStroke, OnboardVideoIcon} from '../../assets/svg';
type Props = NativeStackScreenProps<AuthStackParamList, 'Onboard'>;

const data = [
  {
    title: 'Search For The Product',
    text: 'Search for the products you want to either borrow or purchase.',
    image: images.onbordingimg1,
  },
  {
    title: 'Borrow The Product',
    text: `Borrow the product you like and it will be delivered to your door step.`,
    image: images.onbordingimg2,
  },
  {
    title: 'Purchase The Product',
    text: `Purchase the product you like and it will be delivered to your door step. `,
    image: images.onbordingimg3,
  },
  {
    title: 'How it Works?',
    text: `Please, watch the video above to understand how it works.`,
    image: images.onbordingimg4,
  },
];

const OnboardingScreen: React.FC<Props> = ({navigation}) => {
  const {setFirstLaunch} = useUser();
  const sliderRef = useRef<AppIntroSlider | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleStartedbtn = async () => {
    try {
      await AsyncStorage.setItem('appLaunched', 'false');
      setFirstLaunch(false);
      navigation.replace('SignIn');
      console.log('press');
    } catch (error) {
      console.error('Error storing onboarding state:', error);
    }
  };

  const renderItem = ({item}: {item: (typeof data)[0]}) => (
    <View style={[styles.slide]}>
      <ImageBackground
        source={item.image}
        style={styles.image}
        imageStyle={{borderRadius: 16}}
        resizeMode="cover">
        {item.title == 'How it Works?' ? (
          <TouchableOpacity
            onPress={() => setIsPlaying(true)}
            style={{marginLeft: 10, marginTop: 10}}>
            <OnboardVideoIcon />
          </TouchableOpacity>
        ) : null}
      </ImageBackground>

      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.text}>{item.text}</Text>
    </View>
  );

  const renderPagination = (activeIndex: number) => (
    <View style={styles.paginationContainer}>
      {activeIndex < data.length - 1 ? (
        <TouchableOpacity
          onPress={handleStartedbtn}
          style={[styles.circleButton, {backgroundColor: colors.white}]}>
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      ) : (
        <View style={{width: 42}} />
      )}
      <View style={styles.paginationDots}>
        {data.map((_, i) => (
          <TouchableOpacity
            key={i}
            style={[i === activeIndex ? styles.activeDot : styles.dot]}
            onPress={() => sliderRef.current?.goToSlide(i, true)}
          />
        ))}
      </View>
      <TouchableOpacity
        style={styles.circleButton}
        onPress={() => {
          activeIndex < data.length - 1
            ? sliderRef.current?.goToSlide(activeIndex + 1, true)
            : handleStartedbtn();
        }}>
        <BackArrowStroke />
      </TouchableOpacity>
      {/* <View style={styles.buttonContainer}>
        {activeIndex < data.length - 1 ? (
          <>
            <TouchableOpacity
              style={[styles.button, styles.skipButton]}
              onPress={handleStartedbtn}>
              <Text style={styles.buttonText}>Skip</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.button}
              onPress={() =>
                sliderRef.current?.goToSlide(activeIndex + 1, true)
              }>
              <Text style={styles.buttonText}>Next</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity style={styles.button} onPress={handleStartedbtn}>
            <Text style={styles.buttonText}>Get Started</Text>
          </TouchableOpacity>
        )}
      </View> */}
    </View>
  );

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <AppIntroSlider
        keyExtractor={item => item.title}
        renderItem={renderItem}
        renderPagination={renderPagination}
        data={data}
        ref={sliderRef}
        onSlideChange={index => setActiveIndex(index)}
      />
      <OnboardingVideoModal
        isVisible={isPlaying}
        onClose={() => setIsPlaying(false)}
      />
    </View>
  );
};

export default OnboardingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 32,
    backgroundColor: colors.white,
  },
  slide: {
    flex: 1,
    paddingHorizontal: 32,
  },
  image: {
    width: '100%',
    height: Dimensions.get('screen').height / 2.3,
    borderRadius: 16,
    overflow: 'hidden',
  },
  text: {
    color: '#464646',
    textAlign: 'center',
    fontFamily: fonts.Regular,
    fontSize: 17,
    marginHorizontal: 28,
    marginTop: 6,
  },
  title: {
    fontSize: 28,
    textAlign: 'center',
    color: colors.primary,
    fontFamily: fonts.SemiBold,
    marginTop: Dimensions.get('screen').height / 40,
    marginHorizontal: 66,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? '7%' : '5%',
    left: '7%',
    right: '7%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  paginationDots: {
    height: 16,
    margin: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 20,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    backgroundColor: `${colors.primary}20`,
  },
  activeDot: {
    width: 48,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    backgroundColor: colors.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginHorizontal: 12,
    gap: 24,
    marginTop: 16,
  },
  button: {
    flex: 1,
    borderRadius: 24,
    backgroundColor: colors.primary,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  skipButton: {
    backgroundColor: '#023e3f',
    flex: 1,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.SemiBold,
  },
  circleButton: {
    width: 42,
    height: 42,
    borderRadius: 42 / 2,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  skipText: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.Medium,
  },
});
