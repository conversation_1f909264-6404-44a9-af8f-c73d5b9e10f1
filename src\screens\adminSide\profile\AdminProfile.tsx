import {StyleSheet, View, SafeAreaView} from 'react-native';
import React, {useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';
import {AuthHeader, ProfileContainer, ProfileItem} from '../../../component';
import {colors} from '../../../utilities/theme';

import ConfirmationModal from '../../../model/ConfirmationModal';
import {useUser} from '../../../Hooks/UseContext';
import {
  LogoutIcon,
  ProfileChatIcon,
  ProfileSettingsIcon,
  SecurityIcon,
} from '../../../assets/svg';
import useAuth from '../../../Hooks/UseAuth';
import CustomHeader from '../../../component/common/CustomHeader';
type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'AdminProfile'
>;

const AdminProfile: React.FC<Props> = ({navigation}) => {
  const {logOut} = useAuth();
  const {setUser, user} = useUser();
  const [isLogOutModel, setLogOutModal] = useState(false);

  return (
    <View style={styles.container}>
      <ProfileContainer onPress={() => navigation.navigate('EditProfile')} />
      <ProfileItem
        title="Change Password"
        icon={SecurityIcon}
        onPress={() => navigation.navigate('ChangePassword')}
      />
      <ProfileItem
        title="Customer Support"
        icon={ProfileChatIcon}
        onPress={() => navigation.navigate('UserListChat')}
      />

      <ProfileItem
        title="Logout"
        icon={LogoutIcon}
        onPress={() => setLogOutModal(true)}
      />
      <ConfirmationModal
        isVisible={isLogOutModel}
        onClose={() => setLogOutModal(false)}
        onPress={() => {
          if (user?.userId) {
            logOut(user.userId);
          }
          setLogOutModal(false);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 28,
    backgroundColor: colors.white,
  },
});

export default AdminProfile;
