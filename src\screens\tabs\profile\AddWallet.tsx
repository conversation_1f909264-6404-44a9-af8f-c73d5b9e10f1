import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import React, {useState} from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AppButton, AuthHeader, FormInput} from '../../../component';
import {colors, fonts} from '../../../utilities/theme';
import ToggleSwitch from 'toggle-switch-react-native';
import {DownArrow} from '../../../assets/svg';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'AddWallet'
>;
const AddWallet: React.FC<Props> = ({navigation}) => {
  const [isToggled, setIsToggled] = useState(false);
  const toggleButton = () => {
    setIsToggled(!isToggled);
  };

  return (
    <View style={styles.container}>
      <AuthHeader
        backArrow={true}
        onPress={() => navigation.goBack()}
        title="Add a Wallet"
        titleStyle={{marginLeft: 30}}
        arrowContainerStyle={{marginHorizontal: 2}}
      />
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust behavior based on platform
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0} // Offset for iOS to handle status bar height
      >
        <ScrollView
          contentContainerStyle={{flexGrow: 1, paddingBottom: 40}}
          showsVerticalScrollIndicator={false}>
          <View style={{marginBottom: 40}} />
          <View style={{marginHorizontal: 2}}>
            <Text style={styles.headerText}>Enter Informaton</Text>

            <Text style={styles.labelStyle}>Select a Wallet</Text>
            <TouchableOpacity
              style={styles.selectWalletContainer}
              onPress={() => navigation.navigate('SelectWallet')}>
              <Text style={styles.selectBankText}> Select a Wallet </Text>
              <DownArrow />
            </TouchableOpacity>

            <Text style={styles.labelStyle}>Account Holder</Text>
            <FormInput
              inputContainerStyle={{marginTop: 0, marginBottom: 30}}
              placeholder="Enter account holder name"
              placeholderTextColor={colors.gray[200]}
            />

            <View style={styles.toggleContainer}>
              <ToggleSwitch
                isOn={isToggled}
                onColor={colors.primary}
                offColor="#ccc"
                size="medium"
                onToggle={toggleButton}
              />
              <Text style={styles.toggleText}>Mark as default card</Text>
            </View>

            <AppButton
              title="Add Bank"
              onPress={() => navigation.navigate('AddBank')}
              customStyle={{marginTop: 20}}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default AddWallet;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 32,
    backgroundColor: colors.white,
  },

  headerText: {
    fontSize: 22,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginBottom: 20,
  },

  selectWalletContainer: {
    height: 57,
    borderRadius: 15,
    marginHorizontal: 2,
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    alignItems: 'center',
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,
    elevation: 5,
    marginBottom: 30,
  },

  selectBankText: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.gray[200],
  },

  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.black,
    marginBottom: 8,
  },

  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 25,
  },

  toggleText: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    marginLeft: 10,
  },
});
