import { StyleSheet, Text, View, TextInputProps, TextInput, Image, ViewStyle, TextStyle } from "react-native";
import React from "react";
import { colors, fonts } from "../../utilities/theme";
import { images } from "../../assets/images";
import PhoneInput from "react-native-phone-number-input";
import { CountryCode } from "react-native-country-picker-modal";
interface Props extends TextInputProps {
  label?: string;
  placeholder?: string;
  showImage?: boolean;
  inputContainerStyle?: ViewStyle;
  inputStyles?: TextStyle;
  value?: string; // Add value prop
  defaultCountryValue?: CountryCode;
  onChangeText?: (text: string) => void;
  onFocusInput?: () => void;
  phonePicker?: boolean;
  googlePlacesInput?: boolean;
  errorMessage?: string | false;
  editable?: boolean;
}

const ProfileInput: React.FC<Props> = ({
  label,
  placeholder,
  showImage,
  multiline = false,
  inputContainerStyle,
  inputStyles,
  value,
  defaultCountryValue,
  onChangeText,
  onFocusInput,
  keyboardType = "default",
  phonePicker = false,
  errorMessage,
  editable,
}) => {
  // const getPhoneWithoutCountryCode = (phoneNumber: string) => {
  //   return phoneNumber.startsWith('+') ? phoneNumber.slice(3) : phoneNumber;
  // };
  // const displayPhoneNumber = getPhoneWithoutCountryCode(value ? value : '');
  // console.log('val', value);
  return (
    <View style={{ marginBottom: 16 }}>
      {phonePicker ? (
        <>
          {label && <Text style={styles.inputLabel}>{label}</Text>}
          <View style={[styles.inputContainer, { backgroundColor: "#f6f6f6", paddingLeft: 0 }, inputContainerStyle]}>
            <PhoneInput
              defaultCode={defaultCountryValue}
              layout="second"
              value={value}
              onChangeFormattedText={onChangeText}
              countryPickerProps={{ withCallingCodeButton: true }}
              textInputProps={{
                placeholder: placeholder,
                onFocus(e) {
                  onFocusInput && onFocusInput();
                },
              }}
              containerStyle={styles.pickerContainer}
              textContainerStyle={{
                backgroundColor: "#f6f6f6",
                borderLeftWidth: 1,
                borderLeftColor: colors.gray[100],
              }}
              textInputStyle={[[styles.textInput, { padding: 0 }, inputStyles]]}
            />
            <Image tintColor={colors.primary} source={images.EdirIcon} style={{ width: 14, height: 14 }} />
          </View>
          {errorMessage && <Text style={styles.errorText}>{errorMessage}</Text>}
        </>
      ) : (
        <>
          {label && <Text style={styles.inputLabel}>{label}</Text>}
          <View style={[styles.inputContainer, { backgroundColor: "#f6f6f6" }, inputContainerStyle]}>
            <TextInput
              style={[styles.textInput, inputStyles]}
              placeholder={placeholder}
              placeholderTextColor={showImage ? "#A8A8A8" : "#787878"}
              multiline={multiline}
              numberOfLines={multiline ? 10 : 1}
              value={value}
              onChangeText={onChangeText}
              keyboardType={keyboardType}
              onFocus={() => onFocusInput && onFocusInput()}
              editable={editable}
            />
            {showImage && (
              <Image tintColor={colors.primary} source={images.EdirIcon} style={{ width: 14, height: 14 }} />
            )}
          </View>
          {errorMessage && <Text style={styles.errorText}>{errorMessage}</Text>}
        </>
      )}
    </View>
  );
};

export default ProfileInput;

const styles = StyleSheet.create({
  inputLabel: {
    fontSize: 14,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginBottom: 6,
    marginLeft: 16,
  },

  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 15,
    paddingHorizontal: 26,
    height: 55,
    width: "100%",
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  pickerContainer: {
    alignItems: "center",
    flex: 1,
    backgroundColor: "#f6f6f6",
    borderTopLeftRadius: 15,
    borderBottomLeftRadius: 15,
  },
  errorText: {
    color: colors.red2,
    marginTop: 5,
    fontSize: 12,
    fontFamily: fonts.Medium,
  },
});
