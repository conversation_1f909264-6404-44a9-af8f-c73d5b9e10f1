import { StyleSheet, View, Text, FlatList, ActivityIndicator, Dimensions } from "react-native";
import React, { useEffect, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AdminBottomTabParamlist } from "../../../navigation/AdminBottomTabs";
import { colors } from "../../../utilities/theme";
import firestore from "@react-native-firebase/firestore";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import { IOrders } from "../../../interfaces/IOrders";
import { IUser } from "../../../interfaces/IUser";
import ManagementItem from "../../../component/adminCommon/ManagementItem";

type Props = NativeStackScreenProps<AdminBottomTabParamlist & AdminStackParamsList, "Management">;

interface IExtendedOrders extends Omit<IOrders, "products"> {
  orders: IOrders[];
  user?: IUser;
}

const Management: React.FC<Props> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<IExtendedOrders[]>([]);

  useEffect(() => {
    const unsubscribe = firestore()
      .collection("Orders")
      .orderBy("createdAt", "desc")
      .onSnapshot(async (snapshot) => {
        const updatedOrders: IExtendedOrders[] = [];

        for (const orderDoc of snapshot.docs) {
          const orderData = orderDoc.data() as IExtendedOrders;

          if (updatedOrders.some((order) => order.orderId === orderData.orderId)) continue;

          let userData: IUser | undefined = undefined;
          if (orderData.userId) {
            const userDoc = await firestore().collection("Users").doc(orderData.userId).get();
            if (userDoc.exists) {
              userData = { userId: userDoc.id, ...userDoc.data() } as IUser;
            }
          }

          updatedOrders.push({ ...orderData, user: userData });
        }

        setOrders(updatedOrders);
        setIsLoading(false);
      });

    return unsubscribe;
  }, []);

  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
      ) : (
        <FlatList
          data={orders}
          contentContainerStyle={styles.listContentContainer}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item) => item.orderId}
          renderItem={({ item }) => (
            <ManagementItem
              item={item}
              onPressCard={() =>
                navigation.navigate("AdminOrderDetails", {
                  productIds: item.productIds.map((p) => p.productId),
                  orderId: item.orderId,
                })
              }
            />
          )}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No orders placed yet. Please check back later.</Text>
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
  },
  loader: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    height: Dimensions.get("window").height / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  listContentContainer: {
    paddingTop: 20,
    paddingBottom: 16,
  },
});

export default Management;
