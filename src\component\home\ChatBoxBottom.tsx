import {
  StyleSheet,
  ViewStyle,
  TextInputProps,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  View,
} from "react-native";
import React from "react";
import { colors, fonts } from "../../utilities/theme";
import { images } from "../../assets/images";
import { IUser } from "../../interfaces/IUser";
interface Props extends TextInputProps {
  containerStyle?: ViewStyle;
  onPress?: () => void;
  admin?: IUser;
}

const ChatBoxBottom: React.FC<Props> = ({ containerStyle, onPress, admin }) => {
  return (
    <TouchableOpacity style={[styles.bottomContainer, containerStyle]} onPress={onPress}>
      {admin?.name ? (
        <>
          <Text numberOfLines={1} style={styles.bottomText}>
            Hey, {admin?.name ? admin.name.split(" ")[0] : "Admin"}
          </Text>
          <Image
            style={{ width: 36, height: 36 }}
            // source={admin?.profileImage ? { uri: admin.profileImage } : images.Avatar}
            source={images.Logo}
            resizeMode="contain"
          />
        </>
      ) : (
        <ActivityIndicator
          size={"small"}
          color={colors.primary}
          style={{ paddingVertical: 5, paddingHorizontal: 20 }}
        />
      )}
    </TouchableOpacity>
  );
};

export default ChatBoxBottom;

const styles = StyleSheet.create({
  bottomContainer: {
    position: "absolute",
    backgroundColor: colors.white,
    right: 28,
    bottom: 25,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    width: 150,
    height: 56,
    justifyContent: "center",
    paddingHorizontal: 12,
  },

  bottomText: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: colors.gray[250],
    width: 120,
    flex: 1,
  },
});
