import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  TextInput,
  Clipboard
} from 'react-native';
import {
  generateDeepLink,
  parseDeepLink,
  shareDeepLink,
  openDeepLink,
  getDeepLinkUrls
} from '../utilities/deepLinkUtils';
import { runAllTests } from '../utilities/deepLinkTests';

const DeepLinkTester = () => {
  const [testUrl, setTestUrl] = useState('voitto://product/12345');
  const [testResults, setTestResults] = useState('');

  const quickTests = [
    { name: 'Product', screen: 'ProductDetails', params: { id: '12345' } },
    { name: 'Chat', screen: 'ChatDetails', params: { recipientId: 'user123' } },
    { name: 'Order', screen: 'OrderTracking', params: { orderId: 'order456' } },
    { name: 'Notifications', screen: 'Notification', params: {} },
    { name: 'Home', screen: 'Home', params: {} },
  ];

  const handleGenerateLink = (screen, params) => {
    const urls = getDeepLinkUrls(screen, params);
    Alert.alert(
      'Generated Links',
      `Custom: ${urls.customScheme}\n\nWeb: ${urls.webUrl}`,
      [
        { text: 'Copy Custom', onPress: () => Clipboard.setString(urls.customScheme) },
        { text: 'Copy Web', onPress: () => Clipboard.setString(urls.webUrl) },
        { text: 'Close' }
      ]
    );
  };

  const handleParseLink = () => {
    const parsed = parseDeepLink(testUrl);
    if (parsed) {
      Alert.alert(
        'Parsed Link',
        `Screen: ${parsed.screen}\nParams: ${JSON.stringify(parsed.params, null, 2)}`
      );
    } else {
      Alert.alert('Error', 'Could not parse the URL');
    }
  };

  const handleShareLink = async (screen, params) => {
    try {
      const result = await shareDeepLink(
        screen,
        params,
        'Check this out!',
        'I found something interesting on Voitto!'
      );
      Alert.alert('Share Result', result ? 'Shared successfully!' : 'Share cancelled or failed');
    } catch (error) {
      Alert.alert('Share Error', error.message);
    }
  };

  const handleOpenLink = async () => {
    try {
      const result = await openDeepLink(testUrl);
      Alert.alert('Open Result', result ? 'Link opened successfully!' : 'Could not open link');
    } catch (error) {
      Alert.alert('Open Error', error.message);
    }
  };

  const runTests = () => {
    console.log('Running deep link tests...');
    const results = runAllTests();
    const resultText = `Tests completed!\nPassed: ${results.passed}\nFailed: ${results.failed}\nSuccess Rate: ${results.successRate.toFixed(1)}%\n\nCheck console for detailed results.`;
    setTestResults(resultText);
    Alert.alert('Test Results', resultText);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Deep Link Tester</Text>
      
      {/* Quick Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Tests</Text>
        {quickTests.map((test, index) => (
          <View key={index} style={styles.testRow}>
            <Text style={styles.testName}>{test.name}</Text>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, styles.generateButton]}
                onPress={() => handleGenerateLink(test.screen, test.params)}
              >
                <Text style={styles.buttonText}>Generate</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.shareButton]}
                onPress={() => handleShareLink(test.screen, test.params)}
              >
                <Text style={styles.buttonText}>Share</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>

      {/* URL Testing */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test URL</Text>
        <TextInput
          style={styles.input}
          value={testUrl}
          onChangeText={setTestUrl}
          placeholder="Enter deep link URL to test"
          multiline
        />
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, styles.parseButton]}
            onPress={handleParseLink}
          >
            <Text style={styles.buttonText}>Parse</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.openButton]}
            onPress={handleOpenLink}
          >
            <Text style={styles.buttonText}>Open</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Run All Tests */}
      <View style={styles.section}>
        <TouchableOpacity
          style={[styles.button, styles.testButton]}
          onPress={runTests}
        >
          <Text style={styles.buttonText}>Run All Tests</Text>
        </TouchableOpacity>
        {testResults ? (
          <Text style={styles.results}>{testResults}</Text>
        ) : null}
      </View>

      {/* Instructions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Instructions</Text>
        <Text style={styles.instructions}>
          1. Use "Generate" to create deep links for different screens{'\n'}
          2. Use "Share" to test the sharing functionality{'\n'}
          3. Enter a URL in the text field and use "Parse" to test parsing{'\n'}
          4. Use "Open" to test opening URLs (be careful with this){'\n'}
          5. Use "Run All Tests" to execute comprehensive tests{'\n\n'}
          
          Example URLs to test:{'\n'}
          • voitto://product/12345{'\n'}
          • https://voitto.app/chat/user123{'\n'}
          • voitto://notifications{'\n'}
          • https://voitto.app/admin/orders
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  testRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  testName: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 10,
  },
  button: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    minWidth: 70,
    alignItems: 'center',
  },
  generateButton: {
    backgroundColor: '#007AFF',
  },
  shareButton: {
    backgroundColor: '#34C759',
  },
  parseButton: {
    backgroundColor: '#FF9500',
  },
  openButton: {
    backgroundColor: '#FF3B30',
  },
  testButton: {
    backgroundColor: '#5856D6',
    paddingVertical: 15,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 10,
    fontSize: 14,
    minHeight: 60,
  },
  results: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
    fontSize: 12,
    fontFamily: 'monospace',
  },
  instructions: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
});

export default DeepLinkTester;
