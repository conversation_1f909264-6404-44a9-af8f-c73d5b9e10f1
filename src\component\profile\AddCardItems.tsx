import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Dimensions,
  Button,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {colors, fonts} from '../../utilities/theme';
import {CardLogo, DownArrowOrange, QuestionIcon} from '../../assets/svg';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {useUser} from '../../Hooks/UseContext';
import RNPickerSelect from 'react-native-picker-select';
import {
  ApplePay,
  presentPaymentSheet,
  usePaymentSheet,
  useConfirmPayment,
  confirmPayment,
} from '@stripe/stripe-react-native';
import AppButton from '../common/AppButton';
import ToggleBtn from './ToggleBtn';
const validationSchema = Yup.object().shape({
  cardNumber: Yup.string()
    .min(14, 'Card Number must be 14 digits.')
    .required('Card Number is required'),
  holderName: Yup.string()
    .min(3, 'Name must be atleast 4 digits.')
    .required('Name is required'),
  month: Yup.string().min(2, 'Atleast 2 digits.').required('Month is required'),
  year: Yup.string().min(2, 'Atleast 2 digits.').required('Year is required'),
  cvc: Yup.string().min(3, 'Atleast 3 digits.').required('CVC is required'),
});

const months = [
  {label: 'Select Month', value: '00'},
  {label: 'January', value: '01'},
  {label: 'February', value: '02'},
  {label: 'March', value: '03'},
  {label: 'April', value: '04'},
  {label: 'May', value: '05'},
  {label: 'June', value: '06'},
  {label: 'July', value: '07'},
  {label: 'August', value: '08'},
  {label: 'September', value: '09'},
  {label: 'October', value: '10'},
  {label: 'November', value: '11'},
  {label: 'December', value: '12'},
];

const AddCardItems = () => {
  const {user} = useUser();

  const formik = useFormik({
    initialValues: {
      cardNumber: '',
      holderName: '',
      month: 0,
      year: 0,
      cvc: '',
    },
    validationSchema: validationSchema,
    enableReinitialize: true,
    onSubmit: values => {
      console.log('button press', values);
    },
  });

  const years = Array.from({length: 100}, (_, i) => {
    const year = new Date().getFullYear() - i;
    return {label: year.toString(), value: year};
  });
  return (
    <View>
      <Text style={styles.headerText}>Enter Information</Text>
      <Text style={styles.labelStyle}>Card Number</Text>
      {/* ------------------------------ */}
      {/* <Button title={'Buy'} onPress={Buy} disabled={loading || !ready}></Button> */}
      {/* ---------------------------- */}
      <View
        style={[
          styles.inputContainer,
          {backgroundColor: '#BB542718', height: 52},
        ]}>
        <TextInput
          placeholder="••• •••• ••• •••• 9979"
          placeholderTextColor={colors.primary}
          style={[styles.input, {letterSpacing: 2}]}
          keyboardType="numeric"
          maxLength={14}
          onChangeText={formik.handleChange('cardNumber')}
          value={formik.values.cardNumber}
          onBlur={formik.handleBlur('cardNumber')}
        />
        <CardLogo />
      </View>
      <Text style={styles.errorText}>
        {formik.touched.cardNumber && formik.errors.cardNumber}
      </Text>

      <Text style={styles.labelStyle}>Card Holder</Text>
      <View style={[styles.inputContainer, {height: 52}]}>
        <TextInput
          placeholder="Enter card holder name"
          placeholderTextColor={'#555555'}
          style={styles.input}
          maxLength={20}
          onChangeText={formik.handleChange('holderName')}
          value={formik.values.holderName}
          onBlur={formik.handleBlur('holderName')}
        />
      </View>
      <Text style={styles.errorText}>
        {formik.touched.holderName && formik.errors.holderName}
      </Text>

      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <View style={{flexDirection: 'column', marginRight: 15}}>
          <Text style={[styles.labelStyle, {marginBottom: 5}]}>
            Expiration Date
          </Text>
          <View style={{flexDirection: 'row', gap: 10}}>
            <View style={{flexDirection: 'column'}}>
              <RNPickerSelect
                onValueChange={value => formik.setFieldValue('month', value)}
                items={months}
                placeholder={{label: 'Select Month', value: '00'}}
                style={{
                  ...pickerSelectStyles,
                  inputAndroidContainer: {
                    marginRight: 10,
                  },
                }}
                Icon={DownArrowOrange}
                useNativeAndroidPickerStyle={false}
              />
              <Text style={styles.errorText}>
                {formik.touched.month && formik.errors.month}
              </Text>
            </View>

            <View style={{flexDirection: 'column'}}>
              <RNPickerSelect
                onValueChange={value => formik.setFieldValue('year', value)}
                items={years}
                placeholder={{label: '0', value: 0}}
                value={formik.values.year}
                style={pickerSelectStyles}
                Icon={DownArrowOrange}
                useNativeAndroidPickerStyle={false}
              />
              <Text style={styles.errorText}>
                {formik.touched.year && formik.errors.year}
              </Text>
            </View>
          </View>
        </View>
        <View style={{flexDirection: 'column'}}>
          <Text style={[styles.labelStyle, {marginBottom: 5}]}>CVC</Text>
          <View
            style={[
              styles.inputContainer,
              {width: Dimensions.get('window').width / 3.9},
            ]}>
            <TextInput
              placeholder="0"
              style={styles.input}
              maxLength={3}
              keyboardType="numeric"
              onChangeText={formik.handleChange('cvc')}
              value={formik.values.cvc}
              onBlur={formik.handleBlur('cvc')}
            />
            <TouchableOpacity>
              <QuestionIcon />
            </TouchableOpacity>
          </View>
          <Text style={styles.errorText}>
            {formik.touched.cvc && formik.errors.cvc}
          </Text>
        </View>
      </View>

      <ToggleBtn />
      <AppButton title="Add Card" onPress={formik.handleSubmit} />
    </View>
  );
};

export default AddCardItems;
const styles = StyleSheet.create({
  headerText: {
    fontSize: 24,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginBottom: 10,
  },

  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.black,
    marginBottom: 8,
  },

  inputContainer: {
    height: 40,
    flex: 1,
    borderRadius: 8,
    backgroundColor: colors.gray[120],
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },

  inputStyle: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.black,

    flex: 1,
    paddingLeft: 19,
  },

  input: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.black,
  },

  errorText: {
    color: colors.red2,
    // marginTop: 5,
    marginBottom: 10,
    fontSize: 10,
    fontFamily: fonts.Medium,
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 12,
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: 'gray',
    borderRadius: 4,
    color: 'black',
    paddingRight: 30,
  },

  inputAndroid: {
    width: Dimensions.get('window').width / 4.2,
    fontSize: 14,
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 20,
    backgroundColor: colors.gray[120],
    alignItems: 'center',
    fontFamily: fonts.Regular,
    color: colors.black,
  },
  iconContainer: {
    position: 'absolute',
    top: '50%',
    right: 15,
    transform: [{translateY: -5}],
  },
});
