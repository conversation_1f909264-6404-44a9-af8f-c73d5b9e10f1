import React, {useEffect} from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {AppButton, AuthHeader, FormInput} from '../../component';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import useAuth from '../../Hooks/UseAuth';
type Props = NativeStackScreenProps<AuthStackParamList, 'ForgetPassword'>;

const ForgetPassword: React.FC<Props> = ({navigation}) => {
  const {userForgotPassword, loading} = useAuth();
  const validationSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
  });

  const formik = useFormik({
    initialValues: {
      email: '',
    },

    validationSchema: validationSchema,
    onSubmit: values => {
      const {email} = values;
      userForgotPassword(email, () => navigation.goBack());
    },
  });

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'android' ? 'height' : 'padding'}>
        <SafeAreaView style={{flex: 1}}>
          <ScrollView
            contentContainerStyle={{marginHorizontal: 4}}
            showsVerticalScrollIndicator={false}>
            <View>
              <Text
                style={[
                  appStyles.titleStyle,
                  {marginTop: 50, marginHorizontal: 24},
                ]}>
                Forget Password
              </Text>

              <Text
                style={[
                  appStyles.body4,
                  {
                    fontFamily: fonts.Regular,
                    marginTop: 16,
                    marginHorizontal: 24,
                  },
                ]}>
                To reset your password, Please enter your {''}
                <Text style={{fontFamily: fonts.Medium}}>
                  email adress / phone number{' '}
                </Text>
              </Text>

              <FormInput
                placeholder="Enter email"
                containerStyle={{marginTop: 60, marginHorizontal: 24}}
                keyboardType="email-address"
                onChangeText={formik.handleChange('email')}
                value={formik.values.email}
                onBlur={formik.handleBlur('email')}
                errorMessage={formik.touched.email && formik.errors.email}
              />
              <AppButton
                title="Send Code"
                customStyle={{marginTop: 40, marginHorizontal: 24}}
                onPress={formik.handleSubmit}
                isLoading={loading}
                disabled={!(formik.isValid && formik.dirty)}
              />
              {/* <TouchableOpacity
                onPress={() => navigation.navigate('PhoneVerification')}>
                <Text
                  style={[
                    appStyles.body5,
                    {color: colors.primary, marginTop: 28},
                  ]}>
                  Enter Phone Number Instead.
                </Text>
              </TouchableOpacity> */}
            </View>
          </ScrollView>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ForgetPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // paddingHorizontal: 24,
    paddingBottom: 16,
  },
});
