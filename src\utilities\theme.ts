import { Platform, StyleSheet } from "react-native";

export const colors = {
  primary: "#BB5427",
  secondery: "#fcf6f4",
  black: "#000000",
  blackLight: "#555555",
  white: "#FFFFFF",
  blue: "#2081EF",
  red2: "#FF4242",
  yellow: "#ECA237",
  gray: {
    50: "#555555",
    100: "#D4D3D3",
    120: "#f2f2f2",
    150: "#B8B8B8",
    200: "#8D8A8A",
    250: "#4A4A4A",
    300: "#f6f6f6",
    350: "#9e9e9e",
  },
  secondary: "#BB5427",
  bgcolor: "#FFFFFF",
  pink: "#f7eee9",
};

export const fonts = {
  Bold: "Poppins-Bold",
  SemiBold: "Poppins-SemiBold",
  Medium: "Poppins-Medium",
  Regular: "Poppins-Rergular",
  Light: "Poppins-Light",
};

export const paymentSheetStyles = {
  shapes: {
    borderRadius: 12,
    borderWidth: 0.5,
  },
  primaryButton: {
    shapes: {
      borderRadius: Platform.OS === "android" ? 40 : 20,
    },
  },
  colors: {
    primary: colors.primary,
    background: colors.white,
    componentBackground: "#F3F8FA",
    componentBorder: "#F3F8FA",
    componentDivider: "#000000",
    primaryText: "#000000",
    secondaryText: "#000000",
    componentText: "#000000",
    placeholderText: colors.gray[150],
  },
};

export const appStyles = StyleSheet.create({
  emptyStateText: {
    fontSize: 16,
    fontFamily: fonts.Medium,
    color: colors.black,
    textAlign: "center",
    paddingTop: 80,
  },
  rowBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  flexRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  titleStyle: {
    fontSize: 30,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginTop: 24,
  },
  h1: {
    fontSize: 22,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  h2: {
    fontSize: 20,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  h3: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  h4: {
    fontSize: 16,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  h5: {
    fontSize: 14,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  h6: {
    fontSize: 12,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  body1: {
    fontSize: 22,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  body2: {
    fontSize: 20,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  body3: {
    fontSize: 18,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  body4: {
    fontSize: 16,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  body5: {
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  body6: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
  headerTitleStyle: {
    fontSize: 22,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  iconContainer: {
    width: 34,
    height: 34,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginLeft: 16,
  },
});
