import React, {useEffect} from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  SafeAreaView,
} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../navigation/AuthNavigation';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {AuthHeader, FormInput, OTPInput} from '../../component';
import {useFormik} from 'formik';
import * as Yup from 'yup';

type Props = NativeStackScreenProps<AuthStackParamList, 'OTP'>;

const OTP: React.FC<Props> = ({navigation, route}) => {
  const {title} = route?.params || {};
  const validationSchema = Yup.object().shape({
    OTPNumber: Yup.string()
      .min(4, 'OTP must be at least 4 digits')
      .required('OTP is required'),
  });

  const formik = useFormik({
    initialValues: {
      OTPNumber: '',
    },
    validationSchema: validationSchema,
    onSubmit: values => {
      navigation.navigate('CongratsProfile', {
        title: title ? title : 'Password Change Successfully',
      });
    },
  });

  useEffect(() => {
    if (formik.values.OTPNumber.length === 4) {
      formik.handleSubmit();
    }
  }, [formik.values.OTPNumber]);

  const handleResendCode = () => {
    Alert.alert('Resending...');
  };
  return (
    <View style={styles.container}>
      <SafeAreaView style={{flex: 1}}>
        <ScrollView
          contentContainerStyle={{marginHorizontal: 4}}
          showsVerticalScrollIndicator={false}>
          <View>
            <AuthHeader backArrow={true} onPress={() => navigation.goBack()} />
            <Text style={[appStyles.titleStyle, {marginTop: 50}]}>
              Verify Code
            </Text>

            <Text
              style={[
                appStyles.body4,
                {fontFamily: fonts.Regular, marginTop: 15},
              ]}>
              Please, check your sms inbox, we’ve sent you the code at {''}
              <Text style={{fontFamily: fonts.Medium}}>
                <EMAIL>
              </Text>
            </Text>
            <OTPInput
              setOTPCode={code => formik.setFieldValue('OTPNumber', code)}
            />
            <Text style={[appStyles.body5, {marginTop: 20}]}>(01:07)</Text>

            <Text
              style={[
                appStyles.body5,
                {fontFamily: fonts.Regular, marginTop: 30},
              ]}>
              This session will end in{' '}
              <Text style={styles.bottomText}>02 minutes.</Text>
            </Text>
            <Text style={[appStyles.body5, {fontFamily: fonts.Regular}]}>
              Didn’t receive any code?{' '}
              <Text style={styles.bottomText} onPress={handleResendCode}>
                Resend code
              </Text>
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default OTP;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  bottomline: {
    fontFamily: fonts.Regular,
    marginTop: 28,
  },
  bottomText: {fontFamily: fonts.Medium, color: colors.primary},
});
