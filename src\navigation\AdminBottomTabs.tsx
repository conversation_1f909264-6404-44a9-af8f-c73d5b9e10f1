import * as React from "react";
import { Image, StyleSheet, TouchableOpacity, View, Text } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { images } from "../assets/images";
import { useNavigation } from "@react-navigation/native";
import { appStyles, colors, fonts } from "../utilities/theme";
import { StoreProducts, StoreCategories, Management, Analytics, AdminProfile } from "../screens/adminSide";
import { BackIcon } from "../assets/svg";
export type AdminBottomTabParamlist = {
  StoreProducts: undefined;
  StoreCategories: undefined;
  Management: undefined;
  Analytics: undefined;
  AdminProfile: undefined;
};

const Tab = createBottomTabNavigator<AdminBottomTabParamlist>();

function AdminBottomTabs() {
  const navigation = useNavigation();
  return (
    <Tab.Navigator
      initialRouteName="StoreProducts"
      screenOptions={({ route, navigation }) => ({
        tabBarHideOnKeyboard: true,
        tabBarActiveTintColor: colors.primary,
        tabBarStyle: styles.tabBarStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
        tabBarItemStyle: styles.tabBarItemStyle,
        headerStyle: styles.headerStyle,
        headerTitleAlign: "center",
        headerTitleStyle: appStyles.headerTitleStyle,
        headerShadowVisible: false,
        // headerLeft: () => {
        //   return (
        //     <TouchableOpacity
        //       style={[appStyles.iconContainer, { marginLeft: 32 }]}
        //       activeOpacity={0.7}
        //       onPress={() => navigation.goBack()}
        //     >
        //       <BackIcon />
        //     </TouchableOpacity>
        //   );
        // },
        tabBarShowLabel: false,
        tabBarIcon: ({ color, focused }) => {
          let source;
          switch (route.name) {
            case "StoreProducts":
              source = images.shop;
              color = focused ? colors.primary : colors.black;
              break;
            case "StoreCategories":
              source = images.category;
              color = focused ? colors.primary : colors.black;
              break;
            case "Analytics":
              source = images.analytics;
              color = focused ? colors.primary : colors.black;
              break;
            case "Management":
              source = images.management;
              color = focused ? colors.primary : colors.black;
              break;
            case "AdminProfile":
              source = images.profile;
              color = focused ? colors.primary : colors.black;
              break;
          }
          return (
            <View style={styles.iconContainer}>
              <Image resizeMode="contain" style={[styles.icon, { tintColor: color }]} source={source} />
              {focused ? <View style={styles.underline} /> : <View style={styles.underline2} />}
            </View>
          );
        },
      })}
    >
      <Tab.Screen
        name="StoreProducts"
        component={StoreProducts}
        options={{
          headerTitle: "Store Products",
          headerLeft: () => null,
        }}
      />
      <Tab.Screen name="StoreCategories" component={StoreCategories} options={{ headerTitle: "Store Categories" }} />
      <Tab.Screen name="Analytics" component={Analytics} />
      <Tab.Screen name="Management" component={Management} options={{ headerTitle: "Orders" }} />
      <Tab.Screen name="AdminProfile" component={AdminProfile} options={{ headerTitle: "Profile" }} />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  icon: {
    width: 22,
    height: 22,
  },
  iconContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  underline: {
    width: 12,
    height: 6,
    backgroundColor: colors.primary,
    marginTop: 4,
    borderRadius: 100,
  },
  underline2: {
    width: 12,
    height: 6,
    backgroundColor: colors.white,
    marginTop: 4,
    borderRadius: 100,
  },
  tabBarStyle: {
    height: 70,
    paddingTop: 15,
    paddingBottom: 10,
    backgroundColor: colors.bgcolor,
  },
  tabBarItemStyle: {
    height: 40,
  },
  headerTitleStyle: {
    color: colors.black,
    fontSize: 18,
    fontFamily: fonts.Bold,
    lineHeight: 20,
  },
  tabBarLabelStyle: {
    justifyContent: "center",
  },
  headerStyle: {
    backgroundColor: colors.bgcolor,
  },
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
});

export default AdminBottomTabs;
