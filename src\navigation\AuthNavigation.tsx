import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {Platform, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {appStyles, colors, fonts} from '../utilities/theme';
import {
  Onboard,
  ForgetPassword,
  OTP,
  Register,
  SignIn,
  PhoneVerification,
  CongratsProfile,
} from '../screens/authflow/index';
import {useUser} from '../Hooks/UseContext';
import {BackIcon} from '../assets/svg';

export type AuthStackParamList = {
  SignIn: undefined;
  Onboard: undefined;
  Register: undefined;
  ForgetPassword: undefined;
  PhoneVerification: undefined;
  OTP: {title: string};
  CongratsProfile: {title: string};
};
const AuthStack = createNativeStackNavigator<AuthStackParamList>();
const AuthStackNavigator = () => {
  const {firstLaunch} = useUser();

  return (
    <AuthStack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitle: undefined,
        headerTitleAlign: 'left',
        headerTitleStyle: appStyles.headerTitleStyle,
        headerLeft: () => {
          return (
            <TouchableOpacity
              style={appStyles.iconContainer}
              activeOpacity={0.7}
              onPress={() => navigation.goBack()}>
              <BackIcon />
            </TouchableOpacity>
          );
        },
      })}>
      {firstLaunch && (
        <AuthStack.Screen
          name="Onboard"
          component={Onboard}
          options={{headerShown: false}}
        />
      )}
      <AuthStack.Screen
        name="SignIn"
        component={SignIn}
        options={{
          headerShown: true,
          headerLeft: () => <Text style={styles.title}>Log In</Text>,
          headerTitle: '',
        }}
      />

      <AuthStack.Screen
        name="Register"
        component={Register}
        options={{
          headerShown: true,
          headerLeft: () => <Text style={styles.title}>Register</Text>,
          headerTitle: '',
        }}
      />
      <AuthStack.Screen
        name="ForgetPassword"
        component={ForgetPassword}
        options={{headerShown: true, headerTitle: ''}}
      />
      <AuthStack.Screen
        name="PhoneVerification"
        component={PhoneVerification}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="OTP"
        component={OTP}
        options={{headerShown: false}}
      />
      <AuthStack.Screen
        name="CongratsProfile"
        component={CongratsProfile}
        options={{headerShown: false}}
      />
    </AuthStack.Navigator>
  );
};
export default AuthStackNavigator;
const styles = StyleSheet.create({
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
  arrowStyle: {
    width: 8,
    height: 15,
    marginLeft: 6,
  },
  title: {
    fontSize: 32,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
});
