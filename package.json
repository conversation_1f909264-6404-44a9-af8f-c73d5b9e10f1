{"name": "voitto", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.0.0", "@react-native-firebase/app": "^21.0.0", "@react-native-firebase/auth": "^21.0.0", "@react-native-firebase/firestore": "^21.0.0", "@react-native-firebase/storage": "^21.0.0", "@react-native-google-signin/google-signin": "^13.1.0", "@react-native-picker/picker": "^2.8.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@stripe/stripe-react-native": "^0.41.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "libphonenumber-js": "^1.11.14", "react": "18.3.1", "react-native": "0.75.2", "react-native-app-intro-slider": "^4.0.4", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-dropdown-picker": "^5.4.6", "react-native-echarts-wrapper": "^2.0.0", "react-native-fbsdk-next": "^13.1.3", "react-native-fs": "^2.20.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-charts": "^1.4.53", "react-native-gifted-chat": "^2.6.3", "react-native-google-places-autocomplete": "^2.5.7", "react-native-htmlview": "^0.17.0", "react-native-image-crop-picker": "^0.41.2", "react-native-image-picker": "^7.1.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-otp-entry": "^1.7.1", "react-native-phone-number-input": "^2.1.0", "react-native-picker-select": "^9.3.1", "react-native-popup-menu": "^0.16.1", "react-native-ratings": "^8.1.0", "react-native-reanimated": "^3.15.2", "react-native-render-html": "^6.3.4", "react-native-responsive-linechart": "^5.7.1", "react-native-safe-area-context": "^4.11.0", "react-native-screens": "^3.34.0", "react-native-send-intent": "^1.3.0", "react-native-share": "^11.0.3", "react-native-splash-screen": "^3.3.0", "react-native-star-rating": "^1.1.0", "react-native-stars": "^1.2.2", "react-native-svg": "^15.6.0", "react-native-svg-transformer": "^1.5.0", "react-native-system-navigation-bar": "^2.6.4", "react-native-toast-message": "^2.2.0", "react-native-video": "^6.8.2", "react-native-virtualized-view": "^1.0.0", "react-native-webview": "^13.12.2", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "rn-tourguide": "^3.3.2", "toggle-switch-react-native": "^3.3.0", "victory-chart": "^37.3.1", "victory-native": "^37.3.2", "yarn": "^1.22.22", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.75.2", "@react-native/eslint-config": "0.75.2", "@react-native/metro-config": "0.75.2", "@react-native/typescript-config": "0.75.2", "@types/react": "^18.2.6", "@types/react-native-dotenv": "^0.2.2", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}