import React from "react";
import Navigation from "./src/navigation/Navigation";
import { UserProvider } from "./src/Hooks/UseContext";
import Toast, { BaseToastProps, ErrorToast, SuccessToast } from "react-native-toast-message";
import { StatusBar, StyleSheet } from "react-native";
import { colors } from "./src/utilities/theme";
import { PUBLISH_ABLE_KEY } from "@env";
import { StripeProvider } from "@stripe/stripe-react-native";

const App = () => {
  const toastConfig = {
    error: (props: BaseToastProps) => (
      <ErrorToast
        {...props}
        style={[styles.errorToastContainer, { backgroundColor: colors.white }]}
        text1Style={[styles.text1, { color: colors.black }]}
        text2Style={[styles.text2, { color: colors.black }]}
      />
    ),
    success: (props: BaseToastProps) => (
      <SuccessToast
        {...props}
        style={[styles.successToastContainer, { backgroundColor: colors.white }]}
        text1Style={[styles.text1, { color: colors.black }]}
        text2Style={[styles.text2, { color: colors.black }]}
      />
    ),
  };

  return (
    <StripeProvider
      publishableKey={PUBLISH_ABLE_KEY}
      merchantIdentifier="merchant.identifier"
      urlScheme="your-url-scheme"
    >
      <UserProvider>
        <StatusBar backgroundColor={colors.white} barStyle={"dark-content"} />
        <Navigation />
        <Toast config={toastConfig} onPress={() => Toast.hide()} autoHide={true} visibilityTime={1000} />
      </UserProvider>
    </StripeProvider>
  );
};

const styles = StyleSheet.create({
  successToastContainer: {
    borderLeftColor: "green",
    marginHorizontal: 15,
    width: "88%",
    borderLeftWidth: 9,
  },
  errorToastContainer: {
    borderLeftColor: colors.red2,
    marginHorizontal: 15,
    width: "88%",
    borderLeftWidth: 9,
    backgroundColor: colors.red2,
  },
  text1: {
    fontSize: 16,
    fontWeight: "600",
  },
  text2: {
    fontSize: 13,
    fontWeight: "300",
  },
});

export default App;
