import {
  StyleSheet,
  Text,
  View,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {colors, fonts} from '../../../utilities/theme';
interface Props extends TextInputProps {
  categoryLabel?: string;
  onPressEdit?: () => void;
  onPressRemove?: () => void;
}
const CategoriesItems: React.FC<Props> = ({
  categoryLabel,
  onPressEdit,
  onPressRemove,
}) => (
  <View style={styles.card}>
    <Text style={styles.categoryLabel}>{categoryLabel}</Text>
    <View style={styles.buttonsContainer}>
      <TouchableOpacity style={styles.editButton} onPress={onPressEdit}>
        <Text style={styles.buttonText}>Edit</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.removeButton} onPress={onPressRemove}>
        <Text style={styles.buttonText}>Remove</Text>
      </TouchableOpacity>
    </View>
  </View>
);

export default CategoriesItems;
const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    alignItems: 'center',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    shadowOffset: {width: 0, height: 3},
  },
  categoryLabel: {
    flex: 1, // Allows label to take up remaining space
    // flexShrink: 1,
    // width: '38%',
    fontSize: 14,
    fontFamily: fonts.Bold,
    alignSelf: 'center',
    color: '#333',
    flexWrap: 'wrap',
    marginRight: 10,
  },
  buttonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  editButton: {
    marginRight: 10,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 15,
    alignItems: 'center',
    backgroundColor: colors.black,
  },
  removeButton: {
    backgroundColor: colors.primary,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 15,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 9,
    color: colors.white,
    fontFamily: fonts.SemiBold,
  },
});
