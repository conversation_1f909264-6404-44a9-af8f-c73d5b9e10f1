import { StyleSheet, View, Text, FlatList, SafeAreaView, ActivityIndicator, Dimensions, Button } from "react-native";
import React, { useEffect, useMemo, useState } from "react";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import { App<PERSON><PERSON><PERSON>, AuthHeader } from "../../../component";
import { colors, fonts } from "../../../utilities/theme";
import MyCartItem from "../../../component/mycart/MyCartItem";
import { useUser } from "../../../Hooks/UseContext";
import { IProducts } from "../../../interfaces/Products";
import firestore, { firebase } from "@react-native-firebase/firestore";
import { formatDistanceToNow } from "date-fns";
import { showToast } from "../../../helper/toast";
import { IOrders } from "../../../interfaces/IOrders";
import { ICart, ICartItem } from "../../../interfaces/ICart";
import { usePaymentSheet } from "@stripe/stripe-react-native";
import useStripe from "../../../Hooks/useStripe";
import functions from "@react-native-firebase/functions";
import useNotification from "../../../Hooks/useNotification";
import { ORDER_TRACKING } from "../../../constants/OrderTracking";

type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList, "Store">;

const Store: React.FC<Props> = ({ navigation }) => {
  const { presentPaymentSheet } = usePaymentSheet();
  const { sendNotification } = useNotification();
  const [totalCart, setTotalCarts] = useState(0);
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [orderCreationLoading, setOrderCreationLoading] = useState(false);
  const [myCart, setMyCart] = useState<ICart[]>([]);

  // const totalPrice = useMemo(() => {
  //   return myCart.reduce((sum, item) => {
  //     const price = item?.productData?.price ?? 0;
  //     const borrowingPrice = item?.productData?.borrowingPrice ?? 0;
  //     const borrowingPercentage = item?.productData?.borrowingPercentage ?? 0;
  //     const status = item?.myCart[0]?.status?.toLowerCase();

  //     // If the item is borrowed, use borrowing price instead of full price
  //     if (status === "BORROWED") {
  //       const borrowingAmountOfBorrowingPrice = (borrowingPrice * borrowingPercentage) / 100;
  //       return sum + borrowingAmountOfBorrowingPrice + borrowingPrice;
  //     }

  //     return sum + price;
  //   }, 0);
  // }, [myCart]);

  const totalPrice = useMemo(() => {
    return myCart.reduce((sum, item) => {
      const price = item?.productData?.price ?? 0;
      const borrowingPrice = item?.productData?.borrowingPrice ?? 0;
      const borrowingSecurity = item?.productData?.borrowingSecurity ?? 0;
      const status = item?.myCart[0]?.status?.toLowerCase();

      if (status === "borrowed") {
        // Only use borrowingPrice + borrowingSecurity
        return sum + borrowingPrice + borrowingSecurity;
      }

      return sum + price;
    }, 0);
  }, [myCart]);

  const { initializePaymentSheet, initialLoading, isError } = useStripe({
    customerId: user?.customerId || "",
    amount: totalPrice * 100, // in cents
  });

  const productNames = useMemo(() => {
    const names = myCart.map((item) => item?.productData?.title).filter(Boolean); // remove undefined/null names

    if (names.length === 0) return "";
    if (names.length === 1) return names[0];

    const lastItem = names.pop();
    return `${names.join(" & ")} & ${lastItem}`;
  }, [myCart]);

  const calculateTimeAgo = (date: Date): string => {
    return formatDistanceToNow(date, { addSuffix: true });
  };

  useEffect(() => {
    if (!user) return;
    setIsLoading(true);
    const unsubscribe = firestore()
      .collection("Users")
      .doc(user.userId)
      .onSnapshot(async (doc) => {
        const userData = doc.data();
        if (!userData?.myCart || userData.myCart.length === 0) {
          setIsLoading(false);
          setMyCart([]);
          return;
        }
        const cartItems: ICartItem[] = userData.myCart;
        const productIds = cartItems.map((item) => item.productId);
        try {
          const productsSnapshot = await firestore()
            .collection("Products")
            .where(firestore.FieldPath.documentId(), "in", productIds)
            .get();

          const updatedCart: ICart[] = cartItems.map((cartItem) => {
            const productData = productsSnapshot.docs.find((doc) => doc.id === cartItem.productId)?.data() as IProducts;
            return {
              productData,
              myCart: [
                {
                  productId: cartItem.productId,
                  orderType: cartItem.orderType,
                  createdAt: cartItem.createdAt,
                  quantity: cartItem.quantity,
                },
              ],
              timeAgo: calculateTimeAgo(cartItem.createdAt.toDate()),
            };
          });
          setMyCart(updatedCart);
          setTotalCarts(cartItems.length);
        } catch (error) {
          console.error("Error fetching product data:", error);
        } finally {
          setIsLoading(false);
        }
      });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setMyCart((prevCart) => {
        const updatedCart = prevCart.map((item) => {
          const latestCreatedAt = item.myCart.reduce((latest, cart) => {
            const cartDate = cart.createdAt?.toDate() || new Date();
            return cartDate > latest ? cartDate : latest;
          }, new Date(0));
          const updatedTimeAgo = calculateTimeAgo(latestCreatedAt);
          return {
            ...item,
            timeAgo: updatedTimeAgo,
          };
        });
        return updatedCart;
      });
    }, 60000);
    return () => clearInterval(intervalId);
  }, []);

  const handleDeleteCartItem = async (productId: string) => {
    if (!user) return;
    try {
      const userRef = firestore().collection("Users").doc(user.userId);
      const userSnapshot = await userRef.get();
      if (userSnapshot.exists) {
        const userData = userSnapshot.data() as ICart;
        const cartItemToRemove = userData.myCart.find((item) => item.productId === productId);

        await userRef.update({
          myCart: firestore.FieldValue.arrayRemove(cartItemToRemove),
        });
        showToast(`Product removed from cart successfully!`, "success", "success");
      }
    } catch (error) {
      console.error("Error removing cart item:", error);
    }
  };

  const createOrder = async (paymentIntentId: string) => {
    if (!user) return;

    try {
      let totalPrice = 0;
      let orderTitle = "";
      let totalBorrowingSecurity = 0;
      let hasBorrowedItem = false;
      const productIds: ICartItem[] = [];

      // Loop through each cart group (grouped by seller/product)
      for (const cart of myCart) {
        const product = cart.productData;

        // Loop through each item in the cart group
        for (const item of cart.myCart) {
          if (!product) continue;

          const { price, borrowingPrice = 0, borrowingSecurity = 0, title } = product;

          // === Calculate total price ===
          if (item.orderType === "BORROWED") {
            hasBorrowedItem = true;
            totalBorrowingSecurity += borrowingSecurity ?? 0;
            totalPrice += (borrowingPrice ?? 0) + (borrowingSecurity ?? 0);
          } else if (item.orderType === "PURCHASED" && price) {
            totalPrice += price;
          }

          // === Build order title (joined with & if multiple items) ===
          orderTitle += orderTitle ? " & " + title : title;

          // === Add product info to order ===

          productIds.push({
            productId: item.productId,
            orderType: item.orderType,
            createdAt: item.createdAt,
            quantity: item.quantity,
          });

          // === Decrease product quantity in Products collection ===
          // firestore()
          //   .collection("Products")
          //   .doc(item.productId)
          //   .update({
          //     quantity: firestore.FieldValue.increment(-1),
          //   });
        }
      }

      // === Construct the order object ===
      const orderRef = firestore().collection("Orders").doc();
      const orderData: IOrders = {
        createdAt: firestore.Timestamp.now(),
        status: "PLACED",
        totalPrice,
        userId: user.userId,
        productIds,
        orderTitle,
        orderId: orderRef.id,
        paymentIntentId,
        orderTracking: ORDER_TRACKING,
        ...(totalBorrowingSecurity > 0 ? { totalBorrowingSecurity } : {}),
      };

      // === Save order to Firestore ===
      await orderRef.set(orderData);

      // === Send email and notification ===
      const emailData = {
        email: user.email,
        name: user.name,
        product: productNames, // <-- ensure productNames is defined in your scope
        userId: user.userId,
      };

      // Call Cloud Function to send email/notification
      functions().httpsCallable("sendEmailAndNotificationOnOrder")(emailData);

      // Show local notification for user
      sendNotification({
        title: "Order Place Successfully",
        body: "Congratulations! Your order has been placed successfully.",
        status: "Order Placed",
        userId: user.userId,
      });
      // Show local notification for admins
      const adminSnapshot = await firebase
        .firestore()
        .collection("Users")
        .where("userType", "==", "Admin")
        .limit(1) // Optional: limits to one admin
        .get();

      if (adminSnapshot.empty) {
        throw new Error("No admin user found");
      }

      const adminDoc = adminSnapshot.docs[0];
      const adminId = adminDoc.id;

      sendNotification({
        title: "New Order Received!",
        body: `Order was placed by ${user?.name}.`,
        status: "Order Placed",
        userId: adminId,
      });

      // === Update user document: add order to myOrders & clear cart ===
      firestore()
        .collection("Users")
        .doc(user.userId)
        .update({
          myOrders: firestore.FieldValue.arrayUnion(orderRef.id),
          myCart: [],
        });

      // === Navigate to Orders screen ===
      navigation.navigate("Orders");
    } catch (error) {
      console.error("Error creating order:", error);
    }
  };

  const handlePayPress = async (amount: number) => {
    const { success, paymentIntentId } = await initializePaymentSheet();
    if (!success) return;

    try {
      const { error } = await presentPaymentSheet();
      if (error) {
        console.error(`PaymentSheet Error: ${error.code} - ${error.message}`);
      } else {
        setOrderCreationLoading(true);
        await createOrder(paymentIntentId || "");
      }
    } catch (error) {
      console.error("Error presenting Payment Sheet:", error);
    } finally {
      setOrderCreationLoading(false);
    }
  };

  const ListFooterComponent = ({ totalCart }: { totalCart: number }) => {
    return (
      <View style={{ paddingBottom: 80 }}>
        {totalCart >= 2 && (
          <View style={styles.orderLimitContainer}>
            <Text style={styles.orderHeading}>Order Limit Reached</Text>
            <Text style={styles.orderText}>You can’t have more than 2 products borrowed or purchased.</Text>
          </View>
        )}
        <AppButton
          title="Proceed to checkout"
          onPress={() => handlePayPress(totalPrice)}
          isLoading={initialLoading || orderCreationLoading}
          disabled={initialLoading || orderCreationLoading}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={{
            height: Dimensions.get("window").height / 1.5,
          }}
        />
      ) : myCart.length === 0 ? (
        <Text style={styles.emptyMessage}>Your cart is waiting! Add your favorites and get ready to shop.</Text>
      ) : (
        <FlatList
          contentContainerStyle={{ paddingHorizontal: 32, paddingBottom: 24 }}
          showsVerticalScrollIndicator={false}
          data={myCart}
          renderItem={({ item }) => {
            return (
              <>
                {item.myCart.map((cartItem, index) => (
                  <MyCartItem
                    key={index}
                    btnShowClose={true}
                    heading={item.productData?.title}
                    title={item.productData?.subTitle}
                    imageProp={item.productData?.image}
                    cost={item.productData?.price ?? 0}
                    date={item.timeAgo}
                    btnText={cartItem.orderType === "BORROWED" ? "BORROW" : "PURCHAS"}
                    btnShow={true}
                    onPressClose={() => handleDeleteCartItem(cartItem.productId || "")}
                  />
                ))}
              </>
            );
          }}
          ListFooterComponent={<ListFooterComponent totalCart={totalCart} />}
          keyExtractor={(item) => item.productData?.id || ""}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingTop: 32,
  },

  orderLimitContainer: {
    height: 100,
    backgroundColor: colors.white,
    paddingTop: 15,
    paddingHorizontal: 17,
    borderRadius: 20,
    marginBottom: 24,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 2,
  },

  orderHeading: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.black,
  },

  orderText: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
    textAlign: "center",
    width: "80%",
  },
  emptyMessage: {
    marginHorizontal: 32,
    textAlign: "center",
    marginTop: Dimensions.get("window").height / 3.3,
    fontSize: 16,
    color: colors.gray[50],
  },
});

export default Store;
