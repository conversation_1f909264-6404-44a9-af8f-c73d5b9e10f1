import { Image, StyleSheet, Text, View, TextInputProps, TouchableOpacity, ViewStyle } from "react-native";
import React from "react";
import AppButton from "../common/AppButton";
import { colors, fonts } from "../../utilities/theme";

interface Props extends TextInputProps {
  heading?: string;
  title?: string;
  cost?: number | string;
  date?: string;
  btnText?: string;
  onPressTrack?: () => void;
  imageProp?: string;
  btnShow?: boolean;
  onPressClose?: () => void;
  btnShowClose?: boolean;
  onPressPaymentRefund?: () => void;
  showRefundButton?: boolean;
  loading?: boolean;
}

const MyCartItem: React.FC<Props> = ({
  heading,
  title,
  cost,
  date,
  btnText,
  imageProp,
  onPressTrack,
  btnShow = false,
  onPressClose,
  btnShowClose = false,
  onPressPaymentRefund,
  loading = false,
  showRefundButton = false,
}) => {
  return (
    <View>
      <View style={styles.itemContainer}>
        {btnShowClose && (
          <TouchableOpacity onPress={onPressClose} style={styles.cartValue}>
            <Text style={styles.cartValueText}>x</Text>
          </TouchableOpacity>
        )}
        <View style={styles.row}>
          <View style={styles.textContainer}>
            <Text style={styles.headingText}>{heading}</Text>
            <Text style={styles.titleText}>{title}</Text>
            <View style={styles.buttonsRow}>
              <View style={styles.buttonWrapper}>
                <AppButton
                  activeOpacity={10}
                  title={btnText}
                  titleStyle={styles.btnText}
                  customStyle={
                    [
                      styles.btnContainer,
                      { backgroundColor: btnText === "BORROW" ? colors.black : colors.primary },
                    ] as ViewStyle
                  }
                />
              </View>
            </View>
          </View>
          <View style={styles.imageContainer}>
            <Image style={styles.productImage} source={{ uri: imageProp }} />
          </View>
        </View>
        <View style={styles.costDateContainer}>
          <Text style={styles.costStyle}>$NZ{cost}</Text>
          <Text style={styles.dateStyle}>{date}</Text>
        </View>
        {btnText === "BORROWED" && showRefundButton ? (
          <AppButton
            onPress={onPressPaymentRefund}
            title="Refund Order"
            customStyle={{ height: 38 }}
            titleStyle={{ fontSize: 14, fontFamily: fonts.Medium }}
            isLoading={loading}
            disabled={loading}
          />
        ) : null}
      </View>
    </View>
  );
};

export default MyCartItem;
const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    marginTop: 2,
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginHorizontal: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  cartValue: {
    position: "absolute",
    zIndex: 1,
    top: -2,
    right: -2,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.primary,
    borderRadius: 15,
    width: 18,
    height: 18,
  },
  cartValueText: {
    color: colors.white,
    fontSize: 12,
    lineHeight: 11,
  },
  headingText: {
    fontSize: 18,
    fontFamily: fonts.Bold,
    color: colors.primary,
  },
  titleText: {
    fontSize: 12,
    fontWeight: "300",
    color: colors.gray[50],
  },
  btnContainer: {
    height: 31,
    paddingHorizontal: 20,
    borderRadius: 15,
    alignSelf: "flex-start",
  },
  btnText: {
    fontFamily: fonts.Medium,
    fontSize: 9,
  },
  costDateContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  costStyle: {
    fontSize: 20,
    fontFamily: fonts.Bold,
    color: colors.black,
  },
  dateStyle: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },
  row: {
    flexDirection: "row",
  },
  textContainer: {
    flex: 1,
    marginRight: 10,
  },
  buttonsRow: {
    flexDirection: "row",
    marginTop: 10,
    marginBottom: 5,
  },
  buttonWrapper: {
    flex: 1,
    marginRight: 10,
  },
  centeredButtonWrapper: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  alignSelfStart: {
    alignSelf: "flex-start",
  },
  blackButton: {
    backgroundColor: colors.black,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  imageContainer: {
    flex: 0.5,
  },
  productImage: {
    height: 90,
    width: "100%",
    borderRadius: 12,
  },
  returnOrderText: {
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.black,
  },
});
