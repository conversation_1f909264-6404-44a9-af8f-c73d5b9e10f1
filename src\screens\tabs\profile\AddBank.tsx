import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import React from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AppButton, AuthHeader, FormInput} from '../../../component';
import {colors, fonts} from '../../../utilities/theme';
import {DownArrow} from '../../../assets/svg';
import ToggleBtn from '../../../component/profile/ToggleBtn';
type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'AddBank'
>;
const AddBank: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <AuthHeader
        backArrow={true}
        onPress={() => navigation.goBack()}
        title="Add a Bank"
        titleStyle={{marginLeft: 40}}
        arrowContainerStyle={{marginHorizontal: 2}}
      />
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust behavior based on platform
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0} // Offset for iOS to handle status bar height
      >
        <ScrollView
          contentContainerStyle={{flexGrow: 1, paddingBottom: 40}}
          showsVerticalScrollIndicator={false}>
          <View style={{marginBottom: 40}} />
          <View style={{marginHorizontal: 2}}>
            <Text style={styles.headerText}>Enter Informaton</Text>

            <Text style={styles.labelStyle}>Select a Bank</Text>
            <TouchableOpacity
              style={styles.selectBankContainer}
              onPress={() => navigation.navigate('SelectBank')}>
              <Text style={styles.selectBankText}> Select a Bank </Text>
              <DownArrow />
            </TouchableOpacity>

            <Text style={styles.labelStyle}>Account Holder</Text>
            <FormInput
              inputContainerStyle={{marginTop: 0, marginBottom: 20}}
              placeholder="Enter account holder name"
            />
            <Text style={styles.labelStyle}>Branch Code</Text>
            <FormInput
              inputContainerStyle={{marginTop: 0, marginBottom: 20}}
              placeholder="Enter bank branch code"
            />

            <Text style={styles.labelStyle}>Branch Address</Text>
            <FormInput
              inputContainerStyle={{marginTop: 0, marginBottom: 27}}
              placeholder="Enter bank branch address"
            />
            <ToggleBtn />
            <AppButton
              title="Add Bank"
              customStyle={{marginTop: 15}}
              onPress={() => navigation.navigate('SelectBank')}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default AddBank;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 30,
    backgroundColor: colors.white,
  },

  headerText: {
    fontSize: 24,
    fontFamily: fonts.Bold,
    color: colors.black,
    marginBottom: 23,
  },

  selectBankContainer: {
    height: 57,
    borderRadius: 15,
    backgroundColor: colors.white,
    marginHorizontal: 2,
    paddingHorizontal: 15,
    alignItems: 'center',
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,
    elevation: 2,
    marginBottom: 20,
  },

  selectBankText: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.gray[200],
  },

  labelStyle: {
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.black,
    marginBottom: 8,
  },
});
