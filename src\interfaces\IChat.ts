import {IUser} from './IUser';

interface lastMessage {
  _id: string;
  createdAt: Date;
  read: false;
  sentBy: string;
  sentTo: string;
  text: string;
  user: Array<string>;
  name: string;
  profileImage: string;
  isOnline: boolean;
}

export interface IChat {
  key: string;
  members: Array<string>;
  lastMessage: lastMessage;
  productId: string;
}

export interface IMessage {
  _id: string | number;
  text: string;
  createdAt: Date | number;
  user: IUser;
  image?: string;
  video?: string;
  audio?: string;
  system?: boolean;
  sent?: boolean;
  received?: boolean;
  pending?: boolean;
  quickReplies?: QuickReplies;
}
export interface QuickReplies {
  type: 'radio' | 'checkbox';
  values: Reply[];
  keepIt?: boolean;
}
export interface Reply {
  title: string;
  value: string;
  messageId?: number | string;
}
