import {
  StyleSheet,
  View,
  Text,
  KeyboardAvoidingView,
  ScrollView,
  PermissionsAndroid,
  Image,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import { AdminStackParamsList } from "../../../navigation/AdminNavigation";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthHeader } from "../../../component";
import { appStyles, colors, fonts } from "../../../utilities/theme";
import AdminInput from "../../../component/adminCommon/AdminInput";
import { useFormik } from "formik";
import * as Yup from "yup";
import { images } from "../../../assets/images";
import firestore from "@react-native-firebase/firestore";
import ImagePickerModal from "../../../model/ImagePickerModal";
import storage from "@react-native-firebase/storage";
import { showToast } from "../../../helper/toast";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import { IProducts } from "../../../interfaces/Products";
import { ICategories } from "../../../interfaces/Products";
import DropDownPicker from "react-native-dropdown-picker";
import CustomHeader from "../../../component/common/CustomHeader";
const validationSchema = Yup.object().shape({
  title: Yup.string()
    .min(3, "Title must be at least 3 characters")
    .max(50, "Title cannot exceed 50 characters")
    .required("Title is required"),
  subTitle: Yup.string()
    .min(3, "Subtitle must be at least 3 characters")
    .max(50, "Subtitle cannot exceed 50 characters")
    .required("Subtitle is required"),
  category: Yup.string()
    .min(3, "Category must be at least 3 characters")
    .required("Category is required")
    .notOneOf(["Select a category"], "Please select a valid category"),
  description: Yup.string().min(3, "Description must be at least 3 characters").required("Description is required"),
  price: Yup.number()
    .typeError("Price must be a valid number")
    .required("Price is required")
    .min(1, "Price must be a positive number")
    .max(99999, "Price cannot exceed 5 digits"),

  borrowingSecurity: Yup.number()
    .typeError("Borrowing Security must be a valid number")
    .required("Borrowing Security is required")
    .min(1, "Borrowing Security must be at least 0")
    .max(99999, "Borrowing Security cannot exceed 100000"),
  // quantity: Yup.number()
  //   .required("Quantity is required")
  //   .min(1, "Quantity must be a positive number")
  //   .max(999, "Price cannot exceed 5 digits")
  //   .typeError("Quantity must be a valid number"),

  image: Yup.string().required("Image is required").min(1, "Image must be selected"),
});
type Props = NativeStackScreenProps<AdminStackParamsList, "AddProducts">;
const AddProducts: React.FC<Props> = ({ route, navigation }) => {
  const { product } = route.params || {};
  const [categories, setCategories] = useState<ICategories[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadImg, setUploadImg] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedCategoryid, setSelectedCategoryid] = useState<string>("");
  const [selectedCategoryLabel, setSelectedCategoryLabel] = useState<string>("Select a category");
  // ----------------------   Fetch Categories for dropdown   --------------------------------
  useEffect(() => {
    const unsubscribe = firestore()
      .collection("Categories")
      .onSnapshot(
        (snapshot) => {
          const fetchedCategories = snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          })) as ICategories[];
          setCategories(fetchedCategories);
        },
        (error) => {
          console.error("Error fetching categories:", error);
        },
      );
    return () => unsubscribe();
  }, []);
  // ----------------------   Fetch product by Id   --------------------------------

  const handleSaveChanges = async (values: IProducts) => {
    const uploadedUrl = uploadImg ? await uploadImage() : values.image;
    const payload: IProducts = {
      ...formik.values,
      image: uploadedUrl || values.image,
      category: selectedCategoryid || values.category,
      price: Number(values.price),
      borrowingPrice: Number(values.borrowingPrice),
      borrowingSecurity: Number(values.borrowingSecurity),
      // quantity: Number(values.quantity),
    };
    // If `id` is empty, we assume it's a new product and we set a new ID
    if (!payload.id) {
      payload.id = firestore().collection("Products").doc().id; // Generate a new unique ID if no ID exists
    }
    await updateProducts(payload);
    navigation.goBack();
  };

  const formik = useFormik({
    initialValues: {
      id: product?.id || "",
      title: product?.title || "",
      subTitle: product?.subTitle || "",
      category: product?.category || "",
      description: product?.description || "",
      // quantity: product?.quantity || null,
      price: product?.price || null,
      borrowingPrice: product?.borrowingPrice || null,
      borrowingSecurity: product?.borrowingSecurity || null,
      image: product?.image || "",
      createdAt: product?.createdAt || null,
      updatedAt: product?.updatedAt || null,
    },
    validationSchema: validationSchema,
    enableReinitialize: true,
    onSubmit: handleSaveChanges,
  });

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const onGalleryPress = async () => {
    const result = await launchImageLibrary({
      mediaType: "photo",
      quality: 0.7,
    });

    if (result.didCancel || result.errorCode) {
      toggleModal();
      return;
    }

    if (result.assets && result.assets.length > 0) {
      const selectedImage = result.assets[0];
      if (selectedImage.uri) {
        formik.setFieldValue("image", selectedImage.uri);
        setUploadImg(true);
        toggleModal();
      }
    }
  };

  const requestCameraPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA);
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn(err);
      return false;
    }
  };

  const onCameraPress = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      showToast("Camera permission is required to take pictures.");
      return;
    }

    const result = await launchCamera({
      mediaType: "photo",
      quality: 0.7,
    });

    if (result.didCancel || result.errorCode) {
      toggleModal();
      return;
    }

    if (result.assets && result.assets.length > 0) {
      const capturedImage = result.assets[0];
      if (capturedImage.uri) {
        formik.setFieldValue("image", capturedImage.uri);
        setUploadImg(true);
        toggleModal();
      }
    }
  };

  const uploadImage = async () => {
    setIsLoading(true);
    try {
      const filePath = `Products/${Date.now()}_product_img`;
      const reference = storage().ref(filePath);
      await reference.putFile(formik.values.image || "");
      const url = await reference.getDownloadURL();
      return url;
    } catch (error) {
      showToast("Error uploading image");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProducts = async (values: IProducts) => {
    setIsLoading(true);
    try {
      const currentTime = firestore.Timestamp.now();
      const productRef = firestore().collection("Products").doc(values.id);
      const productDoc = await productRef.get();
      if (productDoc.exists) {
        await productRef.update({
          ...values,
          updatedAt: currentTime,
        });
        setIsLoading(false);
        showToast("Product updated successfully!", "Success", "success");
      } else {
        await productRef.set({
          ...values,
          createdAt: currentTime,
          updatedAt: currentTime,
        });

        setIsLoading(false);
        showToast("Product added successfully!", "Success", "success");
      }
    } catch (error) {
      console.log("error", error);
      showToast(`Could not add or update product details!`);
    }
  };

  useEffect(() => {
    if (selectedCategoryid && categories && Array.isArray(categories)) {
      const matchingCategory: any = categories.find((cat) => cat.id === selectedCategoryid);
      if (matchingCategory) {
        setSelectedCategoryLabel(matchingCategory.categoryLabel);
      } else {
        console.log("Category ID not found in local data");
      }
    }
  }, [selectedCategoryid, categories]);

  useEffect(() => {
    navigation.setOptions({ headerTitle: product?.id ? "Update Product" : "Add Product" });
  }, []);

  return (
    <View style={[styles.container]}>
      <ScrollView
        contentContainerStyle={styles.contentContainerStyle}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
      >
        <KeyboardAvoidingView style={{ flex: 1 }}>
          <Text style={[appStyles.body5, { marginTop: 10 }]}>Product Title</Text>
          <AdminInput
            key="Title"
            placeholder="Product Title"
            inputContainerStyle={{ marginTop: 5 }}
            onChangeText={formik.handleChange("title")}
            value={formik.values.title}
            onBlur={formik.handleBlur("title")}
            errorMessage={formik.touched.title && formik.errors.title}
          />

          <Text style={[appStyles.body5, { marginTop: 10 }]}>Product Label</Text>
          <AdminInput
            key="Subtitle"
            placeholder="Product Label"
            inputContainerStyle={{ marginTop: 5 }}
            onChangeText={formik.handleChange("subTitle")}
            value={formik.values.subTitle}
            onBlur={formik.handleBlur("subTitle")}
            errorMessage={formik.touched.subTitle && formik.errors.subTitle}
          />

          <View style={{ zIndex: dropdownOpen ? 1 : 0 }}>
            <Text style={[appStyles.body5, { marginVertical: 10 }]}>Category</Text>

            <View style={{ height: dropdownOpen ? (categories.length ? 200 : 90) : "auto" }}>
              <DropDownPicker
                open={dropdownOpen}
                value={formik.values.category}
                items={categories.map((category) => ({
                  label: category.categoryLabel,
                  value: category.id,
                }))}
                setOpen={setDropdownOpen}
                setValue={(value) => {
                  formik.setFieldValue("category", value);
                  setSelectedCategoryid(value);
                }}
                placeholder={selectedCategoryLabel}
                style={styles.dropdown}
                dropDownContainerStyle={styles.dropdownContainerStyle}
                listMode="SCROLLVIEW"
                scrollViewProps={{
                  nestedScrollEnabled: true,
                  showsVerticalScrollIndicator: false,
                }}
              />
            </View>
            {formik.touched.category && formik.errors.category && (
              <Text style={styles.error}>{formik.errors.category}</Text>
            )}
          </View>

          <Text style={[appStyles.body5, { marginTop: 10 }]}>Description</Text>
          <AdminInput
            key="Description"
            placeholder="Description"
            onChangeText={formik.handleChange("description")}
            value={formik.values.description}
            onBlur={formik.handleBlur("description")}
            multiline={true}
            numberOfLines={7}
            inputContainerStyle={{ height: 150, marginTop: 10, paddingTop: 2, alignItems: "flex-start" }}
            inputStyles={{ textAlignVertical: "top" }}
            errorMessage={formik.touched.description && formik.errors.description}
            textAlignVertical="top"
          />

          {/* <Text style={[appStyles.body5, { marginTop: 10 }]}>Quantity</Text>
          <AdminInput
            key="Quantity"
            placeholder="Quantity"
            inputContainerStyle={{ marginTop: 5 }}
            onChangeText={formik.handleChange("quantity")}
            value={formik.values.quantity?.toString()}
            onBlur={formik.handleBlur("quantity")}
            keyboardType="numeric"
            errorMessage={formik.touched.quantity && formik.errors.quantity}
          /> */}

          <Text style={[appStyles.body5, { marginTop: 10 }]}>Price</Text>
          <AdminInput
            key="Price"
            placeholder="Price"
            inputContainerStyle={{ marginTop: 5 }}
            onChangeText={formik.handleChange("price")}
            value={formik.values.price?.toString()}
            onBlur={formik.handleBlur("price")}
            keyboardType="numeric"
            errorMessage={formik.touched.price && formik.errors.price}
          />

          <Text style={[appStyles.body5, { marginTop: 10 }]}>Borrowing Price</Text>
          <AdminInput
            key="borrowingPrice"
            placeholder="Borrowing Price"
            inputContainerStyle={{
              marginTop: 5,
              // backgroundColor: "#D4D3D3",
            }}
            inputStyles={{ color: colors.blackLight, fontFamily: fonts.SemiBold }}
            value={formik.values.borrowingPrice?.toString()}
            onChangeText={formik.handleChange("borrowingPrice")}
            keyboardType="numeric"
            // editable={false}
          />

          <Text style={[appStyles.body5, { marginTop: 10 }]}>Borrowing Security</Text>
          <AdminInput
            key="Borrowing Security"
            placeholder="Borrowing Security"
            inputContainerStyle={{ marginTop: 5 }}
            onChangeText={formik.handleChange("borrowingSecurity")}
            value={formik.values.borrowingSecurity?.toString()}
            onBlur={formik.handleBlur("borrowingSecurity")}
            keyboardType="numeric"
            errorMessage={formik.touched.borrowingSecurity && formik.errors.borrowingSecurity}
          />

          <View style={styles.containerUpload}>
            <Text style={styles.labelTextUpload}>Upload Image</Text>
            <TouchableOpacity style={styles.uploadButton} onPress={toggleModal}>
              <Text style={styles.uploadButtonText}>Upload</Text>
            </TouchableOpacity>
          </View>
          <View style={[styles.imageContainer, { borderWidth: formik.values.image ? 0 : 2 }]}>
            {formik.values.image ? (
              <Image style={{ height: 200, width: "100%", borderRadius: 15 }} source={{ uri: formik.values.image }} />
            ) : (
              <View style={{ alignItems: "center" }}>
                <Image
                  source={images.selectImage}
                  style={{
                    height: 30,
                    width: 30,
                  }}
                />
                <Text>Select Image</Text>
              </View>
            )}
          </View>
          {formik.touched.image && formik.errors.image && <Text style={styles.error}>{formik.errors.image}</Text>}
        </KeyboardAvoidingView>
      </ScrollView>
      <View style={styles.btnContainer}>
        <AppButton title="Save" onPress={formik.handleSubmit} isLoading={isLoading} disabled={isLoading} />
      </View>

      <ImagePickerModal
        isVisible={isModalVisible}
        onClose={toggleModal}
        onCameraPress={onCameraPress}
        onGalleryPress={onGalleryPress}
      />
    </View>
  );
};

export default AddProducts;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainerStyle: {
    flexGrow: 1,
    marginHorizontal: 4,
    paddingHorizontal: 28,
    paddingBottom: 20,
  },
  imageContainer: {
    height: 200,
    width: "100%",
    borderRadius: 15,
    borderColor: "#9e9e9e",
    justifyContent: "center",
    alignItems: "center",
  },

  containerUpload: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 10,
    backgroundColor: "#F0F4F8",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    marginVertical: 10,
  },

  labelTextUpload: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
    color: "#4A4A4A",
  },
  uploadButton: {
    backgroundColor: colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 5,
  },
  uploadButtonText: {
    color: "#FFFFFF",
    fontSize: 15,
    fontWeight: "600",
  },

  dropdown: {
    borderRadius: 15,
    backgroundColor: colors.white,
    paddingHorizontal: 20,

    borderColor: "#ddd",
  },
  dropdownContainerStyle: {
    borderColor: "#ddd",

    borderRadius: 15,
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    maxHeight: 160,
  },
  error: {
    fontSize: 12,
    color: "red",
    marginTop: 3,
  },
  btnContainer: {
    paddingVertical: 20,
    paddingHorizontal: 28,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    elevation: 5,
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowRadius: 15,
    shadowOffset: { width: 0, height: -6 },
    backgroundColor: colors.white,
  },
});
