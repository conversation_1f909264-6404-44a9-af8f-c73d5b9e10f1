import {
  StyleSheet,
  View,
  TextInput,
  ViewStyle,
  TextInputProps,
} from 'react-native';
import React from 'react';
import {appStyles, colors, fonts} from '../../utilities/theme';
import {Searchicon} from '../../assets/svg';
interface Props extends TextInputProps {
  containerStyle?: ViewStyle;
  placeholder: string;
  onChangeText?: (text: string) => void;
}

const SearchBar: React.FC<Props> = ({
  placeholder,
  containerStyle,
  onChangeText,
  ...rest
}) => {
  return (
    <View style={[styles.Container, containerStyle]}>
      <Searchicon style={{marginRight: 6}} />
      <TextInput
        style={[styles.inputStyle, {flex: 1}]}
        textAlignVertical="center"
        placeholderTextColor={'#B8B8B8'}
        placeholder={placeholder}
        onChangeText={onChangeText}
        {...rest}
      />
    </View>
  );
};

export default SearchBar;

const styles = StyleSheet.create({
  Container: {
    backgroundColor: colors.white,
    marginBottom: 1,
    borderRadius: 16,
    flexDirection: 'row',
    height: 50,
    paddingHorizontal: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  inputStyle: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: '#555555',
  },
});
