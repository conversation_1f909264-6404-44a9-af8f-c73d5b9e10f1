import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';
import {AppButton} from '../component'; // Assuming you have a reusable AppButton
import {colors, fonts} from '../utilities/theme';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  onCameraPress: () => void;
  onGalleryPress: () => void;
}

const ImagePickerModal: React.FC<Props> = ({
  isVisible,
  onClose,
  onCameraPress,
  onGalleryPress,
}) => {
  return (
    <Modal
      isVisible={isVisible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onBackdropPress={onClose}
      backdropOpacity={0.6}
      // backdropTransitionOutTiming={0}
      // useNativeDriverForBackdrop={true}
      style={{justifyContent: 'flex-end', margin: 0}}>
      <View style={styles.modalContainer}>
        <Text style={styles.title}>Select Image</Text>
        <AppButton
          title="Camera"
          customStyle={StyleSheet.flatten([
            styles.buttonStyle,
            {backgroundColor: colors.primary},
          ])}
          onPress={onCameraPress}
        />
        <AppButton
          title="Gallery"
          customStyle={StyleSheet.flatten([
            styles.buttonStyle,
            {backgroundColor: colors.secondary},
          ])}
          onPress={onGalleryPress}
        />
        <AppButton
          title="Cancel"
          customStyle={StyleSheet.flatten([
            styles.buttonStyle,
            {backgroundColor: colors.white},
          ])}
          titleStyle={{color: colors.gray[200]}}
          onPress={onClose}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    paddingHorizontal: 32,
    paddingVertical: 20,
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 22,
    fontFamily: fonts.Bold,
    color: colors.primary,
    marginBottom: 15,
  },
  buttonStyle: {
    width: '100%',
    marginTop: 15,
  },
});

export default ImagePickerModal;
