import { Timestamp } from "firebase/firestore";
export interface IProducts {
  id: string;
  title: string;
  subTitle: string;
  category: string;
  description: string;
  // quantity: number | null;
  image: string;
  price: number | null;
  borrowingPrice: number | null;
  borrowingSecurity: number | null;
  createdAt: Timestamp | null;
  updatedAt: Timestamp | null;
  reviews?: string[];
  wishList?: string[];
  categoryLabel?: string;
  ratingStar?: number | null;
  ratingReview?: number | null;
  orderType?: "BORROWED" | "PURCHASED"; // "BORROWED" or "PURCHASED"
  refundId?: string;
}

export interface IProductReview {
  reviewId: string;
  productId: string;
  userId: string;
  userName: string;
  userDisplayImage: string;
  ratingStars: number;
  reviewText: string;
  createdAt: Timestamp;
}

export interface ICategories {
  categoryLabel: string;
  id: string;
  createdAt: Timestamp | null;
  updatedAt: Timestamp | null;
}
