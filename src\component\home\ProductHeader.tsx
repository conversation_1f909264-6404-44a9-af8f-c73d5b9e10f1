import {
  StyleSheet,
  View,
  TextInputProps,
  TouchableOpacity,
  Image,
  Text,
  Platform,
} from 'react-native';
import React, {ElementType} from 'react';
import {colors, fonts} from '../../utilities/theme';
import Share from 'react-native-share';
import {
  BackIcon,
  LeftIcon,
  NotificationIcon,
  ShareIcon,
} from '../../assets/svg';
import Navigation from '../../navigation/Navigation';
interface Props extends TextInputProps {
  onPress?: () => void;
  onPres?: () => void;
  onPressShareButton?: () => void;
  title?: string;
  icon?: ElementType;
  backArrow?: boolean;
}

const ProductHeader: React.FC<Props> = ({
  onPress,
  title,
  onPressShareButton,
  onPres,
  backArrow = false,
}) => {
  const ShareButton = () => {
    const options = {
      title: 'Hello World',
      message: 'Check out this awesome content!',
    };

    Share.open(options)
      .then(res => {
        console.log(res);
      })
      .catch(err => {
        if (err) console.log(err);
      });
  };

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Platform.OS === 'android' ? 20 : 40,
      }}>
      <TouchableOpacity
        style={styles.container}
        activeOpacity={0.7}
        onPress={onPress}>
        <BackIcon style={{margin: 10}} />
      </TouchableOpacity>
      {/* ----------------------------------------------- */}
      <TouchableOpacity
        style={styles.container}
        activeOpacity={0.7}
        onPress={onPressShareButton}>
        <ShareIcon style={{margin: 10}} />
      </TouchableOpacity>
    </View>
  );
};

export default ProductHeader;
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    backgroundColor: colors.white,
    // width: 64,
    // height: 45,
    width: 34,
    height: 34,
    borderRadius: 10,
    marginHorizontal: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
  },
});
