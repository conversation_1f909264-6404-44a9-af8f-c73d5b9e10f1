import React from 'react';
import {Text, StyleSheet, View, ViewStyle, Platform} from 'react-native';
import Modal from 'react-native-modal';
import {AppButton} from '../component';
import {colors, fonts} from '../utilities/theme';

interface Props {
  isVisible: boolean;
  onClose: () => void;
  onPress?: () => void;
  title: string;
  subtitle: string;
  isConfirmLoading?: boolean;
  confirmLabel?: string;
  cancelLabel?: string;
}

const ConfirmationModal: React.FC<Props> = ({
  isVisible,
  onClose,
  subtitle, 
  title,isConfirmLoading,
  onPress,
  cancelLabel, 
  confirmLabel
}) => {
  return (
    <Modal
      isVisible={isVisible}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onSwipeComplete={onClose}
      backdropOpacity={0.6}
      swipeDirection={'down'}
      onBackdropPress={onClose}
      backdropTransitionOutTiming={0}
      useNativeDriverForBackdrop={true}
      style={{justifyContent: 'flex-end', margin: 0}}>
      <View style={styles.modalContainer}>
        <Text style={styles.title}>
          {title}
        </Text>
        <Text style={styles.subTitle}>
          {subtitle}
        </Text>

        <AppButton
          title={confirmLabel || "Confirm"}
          customStyle={
            [
              styles.customStyle,
              {
                backgroundColor: colors.primary,
              },
            ] as ViewStyle
          }
          onPress={onPress}
          isLoading={isConfirmLoading}
          disabled={isConfirmLoading}
        />

        <AppButton
          title={cancelLabel || "Cancel"}
          customStyle={{backgroundColor: colors.white}}
          titleStyle={{color: colors.gray[200]}}
          onPress={onClose}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingBottom: Platform.OS === 'android' ? 12 : 24,
  },
  handle: {
    width: '25%',
    height: 6,
    borderRadius: 2,
    backgroundColor: colors.primary,
    marginTop: 10,
  },
  title: {
    fontSize: 22,
    marginTop: 10,
    fontFamily: fonts.Bold,
    color: colors.primary,
  },
  subTitle: {
    color: colors.blackLight,
    fontSize: 16,
    fontFamily: fonts.Medium,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  innerContainer: {
    flexDirection: 'row',
    paddingLeft: 25,
    marginTop: 8,
  },
  warnText: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.red2,
    paddingLeft: 10,
    paddingRight: 30,
  },
  customStyle: {
    width: '100%',
    marginTop: 15,
  },
});

export default ConfirmationModal;
