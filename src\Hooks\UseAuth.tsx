import { useState, useEffect } from "react";
import auth, { firebase } from "@react-native-firebase/auth";
import firestore from "@react-native-firebase/firestore";
import { useUser } from "./UseContext";
import { IUser } from "../interfaces/IUser";
// import {
//   LoginManager,
//   AccessToken,
//   GraphRequest,
//   GraphRequestManager,
//   LoginButton,
// } from 'react-native-fbsdk-next';
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { showToast } from "../helper/toast";
import { Platform } from "react-native";
import functions from "@react-native-firebase/functions";
import { IStripe } from "../interfaces/IStripe";

// import { FacebookSdk } from 'react-native-fbsdk-next'; // Make sure to import Facebook SDK properly

const useAuth = () => {
  const [loading, setLoading] = useState(false);
  const [facebookLoginLoading, setFacebookLoginLoading] = useState(false);
  const [googleLoginLoading, setGoogleLoginLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setUser, fcmToken } = useUser();

  useEffect(() => {
    GoogleSignin.configure({
      scopes: ["email", "profile"],

      webClientId:
        Platform.OS === "android"
          ? "1025338324164-kekj8gedcsntd66f3p25c802s7mbqtf2.apps.googleusercontent.com"
          : "1025338324164-u93klg6n21570e6135140cvs2b8agrd1.apps.googleusercontent.com",
    });
  }, []);

  const createCustomer = async (email: string, name: string) => {
    return functions().httpsCallable("createCustomer")({
      data: {
        email: email.toLowerCase(),
        name,
      },
    });
  };

  const signUp = async (email: string, password: string, name: string) => {
    setLoading(true);
    try {
      // Create user in Firebase Auth
      const userCredential = await auth().createUserWithEmailAndPassword(email.toLowerCase(), password);

      // Create customer in Stripe via Cloud Function
      const res = await createCustomer(email, name);
      const stripeData: IStripe = res.data as IStripe;

      // Create user in Firestore
      await createUserInFirestore(userCredential.user.uid, email.toLowerCase(), name, stripeData.id, "User");
    } catch (error) {
      const firebaseError = error as any;
      if (firebaseError.code === "auth/email-already-in-use") {
        showToast("This email is already registered. Please sign in or use a different email.", "Error");
      } else if (firebaseError.code === "auth/invalid-email") {
        showToast("The email address you entered is invalid. Please provide a valid email address.", "Error");
      } else {
        console.error("Sign-up error:", firebaseError);
        showToast("Something went wrong. Please try again later.", "Error");
      }
    } finally {
      setLoading(false);
    }
  };

  const createUserInFirestore = async (
    uid: string,
    email: string,
    name: string,
    customerId: string,
    userType: "Admin" | "User" = "User",
  ) => {
    try {
      const payload = {
        userId: uid,
        email,
        name,
        userType,
        isOnline: true,
        isTourViewed: false,
        customerId,
        fcmToken, // make sure fcmToken is defined in scope
      };

      await firestore()
        .collection("Users")
        .doc(uid)
        .set(payload)
        .then(() => {
          // Set user in local state
          setUser(payload);
          showToast("Account successfully created!", "Success", "success");
        });

      showToast("Success", "Your account has been successfully created!", "success");
    } catch (error) {
      console.error("Error creating user in Firestore:", error);

      showToast("Error", "There was an issue creating your account. Please try again later.", "error");
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const userCredential = await auth().signInWithEmailAndPassword(email, password);

      let fetchedUser = await fetchUser(userCredential.user.uid);
      if (fetchedUser) {
        const { userId } = fetchedUser;
        setUser(fetchedUser);
        const payload: { isOnline: boolean; fcmToken?: string } = {
          isOnline: true,
        };

        if (fcmToken && fcmToken?.trim() !== "") {
          payload.fcmToken = fcmToken;
        }

        firestore().collection("Users").doc(userId).update(payload);
        showToast("User signed in successfully!", "Success", "success");
      } else {
        showToast("User not found in the database. Please sign up.", "Error");
      }
    } catch (error) {
      console.log(error);

      const firebaseError = error as any;
      if (firebaseError.code === "auth/user-not-found") {
        showToast("No user found with this email. Please check your email or sign up.", "Error");
      } else if (firebaseError.code === "auth/wrong-password") {
        showToast("Incorrect password. Please try again.", "Error");
      } else if (firebaseError.code === "auth/invalid-email") {
        showToast("The email address you entered is invalid. Please provide a valid email address.", "Error");
      } else {
        showToast("Something went wrong. Please try again.", "Error");
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchUser = async (userId: string): Promise<IUser | null> => {
    try {
      const userDoc = await firestore().collection("Users").doc(userId).get();
      if (userDoc.exists) {
        const userData = userDoc.data() as IUser;
        return userData;
      } else {
        console.log("No user found with this ID.");
        return null;
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      return null;
    }
  };

  // const signInWithFacebook = async () => {
  //   setFacebookLoginLoading(true);
  //   try {
  //     const currentUser = auth().currentUser;
  //     if (currentUser) {
  //       await auth().signOut();
  //     }

  //     const result = await LoginManager.logInWithPermissions(['email']);
  //     if (result.isCancelled) {
  //       showToast('Facebook login cancelled. Please try again.', 'Error');
  //       console.log('Login cancelled');
  //       setFacebookLoginLoading(false);
  //       return;
  //     }

  //     const data = await AccessToken.getCurrentAccessToken();
  //     if (!data) {
  //       showToast('Failed to get access token. Please try again.', 'Error');
  //       console.log('Failed to get access token');
  //       setFacebookLoginLoading(false);
  //       return;
  //     }

  //     const facebookCredential = auth.FacebookAuthProvider.credential(
  //       data.accessToken,
  //     );
  //     const userCredential = await auth().signInWithCredential(
  //       facebookCredential,
  //     );
  //     const user = userCredential.user;

  //     const {uid, email} = user || {};
  //     let fetchedUser = await fetchUser(uid);
  //     if (!fetchedUser) {
  //       await createUserInFirestore(uid, email || '', '');
  //       setUser({
  //         userId: uid,
  //         email: email || '',
  //         userType: 'User',
  //       });
  //       setFacebookLoginLoading(false);
  //     } else {
  //       setUser(fetchedUser);
  //       await firestore().collection('Users').doc(uid).update({isOnline: true});
  //       showToast('Logged in successfully!', 'Success', 'success');
  //       setFacebookLoginLoading(false);
  //     }
  //   } catch (error) {
  //     setFacebookLoginLoading(false);
  //     showToast(
  //       'An error occurred during Facebook Sign-In. Please try again.',
  //       'Error',
  //     );
  //     console.error('Error during Facebook Sign-In:', error);
  //   }
  // };

  const signInWithGoogle = async () => {
    setGoogleLoginLoading(true);
    try {
      // await GoogleSignin.signOut();
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      const { data } = response;
      const idToken = data?.idToken;
      if (!idToken) {
        console.log("id token not found");
        setGoogleLoginLoading(false);
        return;
      }
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      const userCredential = await auth().signInWithCredential(googleCredential);

      const firebaseUser = userCredential.user;
      const { uid, email, displayName }: any = firebaseUser;
      let fetchedUser = await fetchUser(uid);
      if (!fetchedUser) {
        // Create customer in Stripe via Cloud Function
        const res = await createCustomer(email, displayName);
        const stripeData: IStripe = res.data as IStripe;

        await createUserInFirestore(uid, email, displayName, stripeData.id, "User");
        setUser({
          userId: uid,
          email: email,
          name: displayName,
          userType: "User",
          customerId: stripeData.id,
        });
        setGoogleLoginLoading(false);
      } else {
        setUser(fetchedUser);
        await firestore().collection("Users").doc(uid).update({ isOnline: true });
        showToast("Logged in successfully!", "Success", "success");
        setGoogleLoginLoading(false);
      }
    } catch (error) {
      const firebaseError = error as any;
      if (firebaseError.code === "CANCELED") {
        showToast("Login was cancelled. Please try again.", "Warning", "warning");
        setGoogleLoginLoading(false);
      } else if (firebaseError.code === "SIGN_IN_REQUIRED") {
        showToast("Sign-in is required. Please try again.", "Error", "error");
        setGoogleLoginLoading(false);
      } else if (firebaseError.code === "NETWORK_ERROR") {
        showToast("Network error. Please check your connection and try again.", "Error", "error");
        setGoogleLoginLoading(false);
      } else {
        showToast("An error occurred during sign-in. Please try again.", "Error", "error");
        setGoogleLoginLoading(false);
      }
    }
  };

  const userForgotPassword = async (email: string, onSuccess: () => void) => {
    setLoading(true);
    try {
      await firebase.auth().sendPasswordResetEmail(email);
      showToast("Reset email sent successfully", "Success", "success");
      onSuccess(); // Call the callback function after successful password reset
    } catch (e) {
      console.error(e);
      showToast("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const logOut = async (userId: string) => {
    try {
      // if (userId) {
      //   await firestore()
      //     .collection('Users')
      //     .doc(userId)
      //     .update({isOnline: false});
      // }
      // const currentUser = auth().currentUser;
      // if (currentUser) {
      //   const providerId = currentUser.providerData[0]?.providerId;
      //   if (providerId === 'facebook.com') {
      //     const accessToken = await AccessToken.getCurrentAccessToken();
      //     if (accessToken) {
      //       const logoutRequest = new GraphRequest(
      //         'me/permissions/',
      //         {accessToken: accessToken.accessToken, httpMethod: 'DELETE'},
      //         (error, result) => {
      //           if (error) {
      //             console.error('Error logging out from Facebook:', error);
      //           } else {
      //             LoginManager.logOut();
      //           }
      //         },
      //       );
      //       new GraphRequestManager().addRequest(logoutRequest).start();
      //     }
      //   } else if (providerId === 'google.com') {
      //     await GoogleSignin.signOut();
      //   }
      // }
      // await auth().signOut();
      setUser({
        userId: "",
        name: "",
        email: "",
        customerId: "", // Add the required customerId property
      });
    } catch (error) {
      console.error("Error during sign out:", error);
    }
  };

  return {
    signUp,
    signIn,
    // signInWithFacebook,
    signInWithGoogle,
    loading,
    googleLoginLoading,
    facebookLoginLoading,
    userForgotPassword,
    error,
    fetchUser,
    logOut,
  };
};

export default useAuth;
