import {StyleSheet, View, ViewStyle, TextInputProps, Text} from 'react-native';
import React from 'react';
import {appStyles, colors, fonts} from '../../utilities/theme';
interface Props extends TextInputProps {
  containerStyle?: ViewStyle;
  onPress?: () => void;
  title: string;
  subtitle: string;
}

const BottomLine: React.FC<Props> = ({
  containerStyle,
  onPress,
  subtitle,
  title,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <Text style={[appStyles.body5, styles.bottomText]}>
        {subtitle}
        <Text onPress={onPress} style={styles.titleContainer}>
          {title}
        </Text>
      </Text>
    </View>
  );
};

export default BottomLine;

const styles = StyleSheet.create({
  container: {alignItems: 'center', justifyContent: 'center'},

  bottomText: {
    fontFamily: fonts.Light,
  },
  titleContainer: {
    color: colors.primary,
    fontFamily: fonts.SemiBold,
    textDecorationLine: 'underline',
  },
});
