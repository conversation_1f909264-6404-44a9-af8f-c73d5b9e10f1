import {StyleSheet, View, Text, FlatList, SafeAreaView} from 'react-native';
import React, {useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {AuthHeader, ProfileItem} from '../../../component';
import {colors} from '../../../utilities/theme';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';

type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'ShowCategoriesModal'
>;

const ShowCategoriesModal: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <SafeAreaView />
      <View style={{paddingHorizontal: 28, paddingBottom: 5}}>
        <AuthHeader
          backArrow={true}
          onPress={() => navigation.goBack()}
          title="Analytics"
          showTitle
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});

export default ShowCategoriesModal;
