import { Linking } from 'react-native';
import { parseDeepLink, isValidDeepLink } from './deepLinkUtils';
import NavigationService from '../navigation/NavigationService';

/**
 * Handle deep link URL parsing and navigation
 * @param {string} url - The deep link URL to handle
 * @param {object} user - Current user object
 */
export const handleDeepLink = (url, user) => {
  if (!url || !isValidDeepLink(url)) return;
  
  console.log("Deep link received:", url);
  
  const parsedLink = parseDeepLink(url);
  if (!parsedLink || !parsedLink.screen) {
    console.log('Could not parse deep link:', url);
    return;
  }
  
  const { screen, params } = parsedLink;
  
  // Check if user needs to be authenticated for this screen
  const authRequiredScreens = [
    'ProductDetails', 'ChatDetails', 'OrderTracking', 'AdminOrderDetails',
    'Notification', 'EditProfile', 'ContactUs', 'Orders', 'Options', 'AddCard',
    'Home', 'Search', 'Profile', 'Cart', 'AdminProfile', 'StoreProducts',
    'StoreCategories', 'Analytics', 'Management', 'AddProducts', 'AddCategories',
    'UserListChat'
  ];
  
  const publicScreens = ['SignIn', 'Register'];
  
  if (authRequiredScreens.includes(screen) && !user?.userId) {
    console.log('User not authenticated for screen:', screen);
    // Store the deep link to handle after login
    // You could store this in AsyncStorage or context
    return;
  }
  
  if (publicScreens.includes(screen) && user?.userId) {
    console.log('User already authenticated, ignoring auth screen:', screen);
    return;
  }
  
  // Handle admin-specific screens
  const adminScreens = [
    'AdminOrderDetails', 'AdminProfile', 'StoreProducts', 'StoreCategories',
    'Analytics', 'Management', 'AddProducts', 'AddCategories', 'UserListChat'
  ];
  
  if (adminScreens.includes(screen) && user?.userType !== 'Admin') {
    console.log('User is not admin for screen:', screen);
    return;
  }
  
  // Navigate to the screen
  try {
    NavigationService.navigate(screen, params);
    console.log('Navigated to:', screen, 'with params:', params);
  } catch (error) {
    console.log('Error navigating to deep link:', error);
  }
};

/**
 * Handle initial deep link when app is opened from a deep link
 * @param {object} user - Current user object
 */
export const handleInitialDeepLink = async (user) => {
  try {
    const initialUrl = await Linking.getInitialURL();
    if (initialUrl) {
      // Add a small delay to ensure navigation is ready
      setTimeout(() => handleDeepLink(initialUrl, user), 1000);
    }
  } catch (error) {
    console.log('Error getting initial URL:', error);
  }
};

/**
 * Set up deep link listener for when app is already running
 * @param {object} user - Current user object
 * @returns {object} Subscription object to remove listener
 */
export const setupDeepLinkListener = (user) => {
  const subscription = Linking.addEventListener('url', ({ url }) => {
    handleDeepLink(url, user);
  });
  
  return subscription;
};

/**
 * Initialize deep linking functionality
 * @param {object} user - Current user object
 * @returns {function} Cleanup function to remove listeners
 */
export const initializeDeepLinking = (user) => {
  // Handle initial deep link
  handleInitialDeepLink(user);
  
  // Set up listener for incoming deep links
  const subscription = setupDeepLinkListener(user);
  
  // Return cleanup function
  return () => {
    if (subscription?.remove) {
      subscription.remove();
    }
  };
};
