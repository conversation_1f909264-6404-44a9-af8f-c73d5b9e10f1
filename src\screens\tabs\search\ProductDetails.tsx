import { NativeStackScreenProps } from "@react-navigation/native-stack";
import React, { useEffect, useMemo, useState } from "react";
import { View, StyleSheet, ImageBackground, TouchableOpacity, Text, ScrollView } from "react-native";
import { HomeStackParamsList } from "../../../navigation/HomeNavigation";
import { AppButton, DescriptionItem, ProductHeader, Rating, RatingItem } from "../../../component";
import { appStyles, colors, fonts } from "../../../utilities/theme";
import { FavoriteIcon, StarIcon, UnFavoriteIcon } from "../../../assets/svg";
import ChatBoxBottom from "../../../component/home/<USER>";
import { BottomTabParamlist } from "../../../navigation/BottomNavigation";
import ShareModal from "../../../model/ShareModel";
import { IProducts } from "../../../interfaces/Products";
import firestore from "@react-native-firebase/firestore";
import UseProduct from "../../../Hooks/UseProduct";
import { useUser } from "../../../Hooks/UseContext";
// import {IProductReview} from '../../../interfaces/IProductReview';
import { IProductReview } from "../../../interfaces/Products";
import BorrowSuccessModal from "../../../model/BorrowSuccessModal";
import BorrowTermsandCondition from "../../../model/BorrowTermsandCondition";
import { IUser } from "../../../interfaces/IUser";
import { shareDeepLink } from "../../../utilities/deepLinkUtils";
type Props = NativeStackScreenProps<BottomTabParamlist & HomeStackParamsList, "ProductDetails">;

const ProductDetails: React.FC<Props> = ({ navigation, route }) => {
  const productData = route.params as IProducts;
  const { id } = route?.params || {};

  const { user } = useUser();
  const { onPressMyCart, onPressWishList } = UseProduct();
  const [borrowLoading, setBorrowLoading] = useState(false);
  const [purchaseLoading, setPurchaseLoading] = useState(false);
  const [product, setProduct] = useState<IProducts>(productData);
  const [productReviews, setProductReviews] = useState<IProductReview[]>([]);
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [admin, setAdmin] = useState<IUser | null>(null);
  useEffect(() => {
    console.log("id-----", productData);
    if (productData.id && productData.title) {
      console.log("its is from the App Flow");
    } else {
      console.log("its is from the Deep Link");
      try {
        firestore()
          .collection("Products")
          .doc(id)
          .get()
          .then((doc) => {
            console.log("doc-----", doc.data());
            if (doc.exists) {
              setProduct(doc.data() as IProducts);
            }
          });
      } catch (error) {
        console.log("error-----", error);
      }
    }
  }, []);
  const memoizedProduct = useMemo(() => product, [product, id]);
  const [rating, setRating] = useState<{
    ratingStar: number;
    ratingReview: number;
  }>({
    ratingStar: memoizedProduct?.ratingStar || 0,
    ratingReview: memoizedProduct?.ratingReview || 0,
  });

  const handlePurchasePress = async (item: IProducts) => {
    setPurchaseLoading(true);
    const updatedMyCart = await onPressMyCart(item, "PURCHASED");
    setPurchaseLoading(false);
    if (updatedMyCart) {
      navigation.navigate("MyCart");
    }
  };

  const [isLogOutModel, setLogOutModal] = useState(false);
  const handleFavoritePress = async (item: IProducts) => {
    const updatedWishList = await onPressWishList(item);
    if (updatedWishList) {
      setProduct({
        ...memoizedProduct,
        wishList: updatedWishList,
      });
    }
  };

  useEffect(() => {
    const unsubscribe = firestore()
      .collection("Users")
      .where("userType", "==", "Admin")
      .limit(1) // Ensures we fetch only one Admin user
      .onSnapshot(
        (querySnapshot) => {
          try {
            if (!querySnapshot.empty) {
              const adminDoc = querySnapshot.docs[0]; // Get the first document
              const adminData = {
                ...adminDoc.data(),
                userId: adminDoc.id,
              } as IUser;

              setAdmin(adminData); // Store a single object instead of an array
            } else {
              setAdmin(null); // No admin found
            }
          } catch (error) {
            console.error("Error processing Admin snapshot: ", error);
          }
        },
        (error) => {
          console.error("Error listening to Admin: ", error);
        },
      );

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const unsubscribe = firestore()
      .collection("Products")
      .doc(memoizedProduct.id)
      .onSnapshot(async (productSnapshot) => {
        const productData = productSnapshot.data();
        if (!productData?.reviews || productData.reviews.length === 0) {
          setProductReviews([]);
          return;
        }
        const reviewIds: string[] = productData.reviews;

        const reviewsQuery = firestore().collection("ProductReviews").where("reviewId", "in", reviewIds);
        const reviewsSnapshot = await reviewsQuery.get();

        const reviews: IProductReview[] = reviewsSnapshot.docs.map(
          (doc) => ({ reviewId: doc.id, ...doc.data() } as IProductReview),
        );
        setProductReviews(reviews);
      });
    return () => unsubscribe();
  }, [memoizedProduct.id]);

  const handleBorrowPress = async (item: IProducts) => {
    setBorrowLoading(true);
    const updatedMyCart = await onPressMyCart(item, "BORROWED");
    setBorrowLoading(false);
    if (updatedMyCart) {
      setSuccessModalVisible(true);
    }
  };

  return (
    <View style={{ backgroundColor: colors.white, flex: 1 }}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 100 }}>
        <ImageBackground
          style={styles.mainImage}
          source={{
            uri: memoizedProduct.image,
          }}
          resizeMode="cover"
        >
          <ProductHeader
            onPress={() => navigation.goBack()}
            onPressShareButton={() => {
              // setLogOutModal(true);
              shareDeepLink("ProductDetails", { id: memoizedProduct.id }, memoizedProduct.title, memoizedProduct.title);
            }}
          />
        </ImageBackground>
        <View style={styles.prductContainer}>
          <View style={styles.textContainer}>
            <View
              style={{
                flexDirection: "column",
                flex: 1,
                marginRight: 10,
              }}
            >
              <Text style={[appStyles.h3, { color: colors.primary }]}>{memoizedProduct.title}</Text>
              <Text style={[appStyles.body6, styles.productLabelText]}>{memoizedProduct.subTitle}</Text>
            </View>
            <TouchableOpacity onPress={() => handleFavoritePress(memoizedProduct)}>
              {!!(memoizedProduct.wishList && user?.userId && memoizedProduct.wishList.includes(user.userId)) ? (
                <FavoriteIcon />
              ) : (
                <UnFavoriteIcon />
              )}
            </TouchableOpacity>
          </View>
          <View style={styles.priceContainer}>
            <View style={{ flexDirection: "row" }}>
              <StarIcon />
              <Text style={styles.ratingText}>{rating.ratingStar?.toFixed(1)}</Text>
              <Text style={styles.reviewText}>{rating.ratingReview} Reviews</Text>
            </View>
            <Text style={[appStyles.h1, { color: colors.primary }]}>$NZ{memoizedProduct.price}</Text>
          </View>
          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={[appStyles.body6, styles.termText]}>Terms & Conditions{""}</Text>
            <Text style={[appStyles.body6, styles.termText]}>Privacy Policy</Text>
            <Text style={[appStyles.body6, styles.termText]}>Terms of Service{""}</Text>
          </View>
          <View style={styles.buttonContainer}>
            <AppButton
              title="Borrow"
              customStyle={{ width: "48%", backgroundColor: colors.black }}
              titleStyle={styles.buttonText}
              onPress={() => {
                setModalVisible(true);
              }}
              isLoading={borrowLoading}
            />

            <AppButton
              title="Purchase"
              customStyle={{ width: "48%" }}
              titleStyle={styles.buttonText}
              onPress={() => handlePurchasePress(memoizedProduct)}
              isLoading={purchaseLoading}
            />
          </View>
        </View>

        <DescriptionItem productDescription={memoizedProduct.description} />
        {productReviews.length > 0 &&
          productReviews.map((review) => (
            <RatingItem
              key={review.reviewId}
              url={review.userDisplayImage}
              name={review.userName}
              rating={review.ratingStars !== undefined ? review.ratingStars : 0}
              reviewText={review.reviewText}
            />
          ))}
        <Rating
          productItem={memoizedProduct}
          onAddNewRating={(ratingReview: number, ratingStar: number) => {
            setRating({
              ratingStar,
              ratingReview,
            });
          }}
        />
      </ScrollView>
      <ChatBoxBottom
        containerStyle={{ right: 32, bottom: 72 }}
        admin={admin ?? undefined}
        onPress={() => admin && navigation.navigate("ChatDetails", { recipientUser: admin })}
      />
      <ShareModal isVisible={isLogOutModel} onClose={() => setLogOutModal(false)} product={memoizedProduct} />
      <BorrowTermsandCondition
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        onCanclePress={() => setModalVisible(false)}
        onPressBorrow={() => {
          setModalVisible(false);
          handleBorrowPress(memoizedProduct);
        }}
        borrowingPrice={memoizedProduct.borrowingPrice ?? 0}
        borrowingSecurity={memoizedProduct.borrowingSecurity ?? 0}
      />
      <BorrowSuccessModal
        borrowSuccessModal={successModalVisible}
        setBorrowSuccessModal={setSuccessModalVisible}
        onPressOk={() => {
          setSuccessModalVisible(false);
          setTimeout(() => {
            navigation.navigate("MyCart");
          }, 300);
        }}
        onBackdropPress={() => {
          setSuccessModalVisible(false);
          setTimeout(() => {
            navigation.navigate("MyCart");
          }, 300);
        }}
        title={memoizedProduct?.title || ""}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  mainImage: {
    height: 290,
    width: "100%",
  },
  prductContainer: {
    marginHorizontal: 28,
    marginBottom: 20,
    borderRadius: 20,
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingBottom: 20,
    marginTop: 20,

    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  priceContainer: {
    marginTop: 5,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },

  productLabelText: {
    color: colors.gray[50],
    flexWrap: "wrap",
  },
  reviewText: {
    fontSize: 12,
    fontFamily: fonts.Medium,
    color: colors.gray[200],
    marginLeft: 11,
  },
  termText: { color: colors.primary, fontFamily: fonts.Medium, fontSize: 10 },
  ratingText: { fontSize: 12, fontFamily: fonts.Bold, marginLeft: 5 },
  buttonText: { fontSize: 14, fontFamily: fonts.SemiBold },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 12,
  },
});

export default ProductDetails;
