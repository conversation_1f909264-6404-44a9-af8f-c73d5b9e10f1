import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Image } from "react-native";
import { appStyles, colors, fonts } from "../../utilities/theme";
import { IOrders } from "../../interfaces/IOrders";
import { IUser } from "../../interfaces/IUser";
import { images } from "../../assets/images";
import moment from "moment";
import { getStatusColor } from "../../helper/getStatusColor";

interface Props {
  item: IOrders & { user?: IUser };
  onPressCard: () => void;
}

const ManagementItem: React.FC<Props> = ({ item, onPressCard }) => {
  return (
    <TouchableOpacity onPress={onPressCard} activeOpacity={0.6} style={styles.itemContainer}>
      <View style={[appStyles.flexRow, styles.header]}>
        <Image
          style={styles.image}
          source={item.user?.profileImage ? { uri: item.user.profileImage } : images.Avatar}
        />
        <View style={{ flex: 1 }}>
          <Text numberOfLines={1} style={styles.userName}>
            {item.user?.name}
          </Text>
          <Text numberOfLines={1} style={styles.userRole}>
            Customer
          </Text>
        </View>
        <View style={[styles.chip, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.chipText}>{item.status}</Text>
        </View>
      </View>

      <View style={styles.row}>
        <Text style={styles.itemLabel}>Total ({item?.productIds?.length}-item)</Text>
        <Text style={styles.price}>NS${item.totalPrice}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Create Time</Text>
        <Text style={styles.label}>{moment(item.createdAt.seconds * 1000).format("MM/DD/YYYY h:mm A")}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Order ID</Text>
        <Text style={styles.label}>{item.orderId}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    marginBottom: 16,
    marginHorizontal: 4,
  },
  header: {
    gap: 12,
    alignItems: "flex-start",
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userName: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.SemiBold,
  },
  userRole: {
    fontSize: 12,
    color: colors.black,
    fontFamily: fonts.Regular,
  },
  chip: {
    backgroundColor: "#526CF9",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  chipText: {
    fontFamily: fonts.Regular,
    fontSize: 10,
    color: colors.white,
  },
  row: {
    ...appStyles.rowBetween,
    paddingTop: 6,
  },
  itemLabel: {
    fontSize: 12,
    color: "#03020A",
    fontFamily: fonts.Medium,
  },
  price: {
    fontSize: 18,
    color: colors.black,
    fontFamily: fonts.SemiBold,
  },
  label: {
    fontSize: 12,
    color: "#979797",
    fontFamily: fonts.Medium,
  },
});

export default ManagementItem;
