import { firebase } from "@react-native-firebase/firestore";
import { useEffect, useState } from "react";
import { useUser } from "./UseContext";
import messaging from "@react-native-firebase/messaging";
import firestore from "@react-native-firebase/firestore";
import { IUser } from "../interfaces/IUser";

type NotificationProps = {
  title: string;
  body: string;
  status: string;
  userId: string;
  recipientId?: string;
};

export default () => {
  const { user } = useUser();
  const [unreadCount, setUnreadCount] = useState(0);

  const sendNotification = async ({ title, body, status, userId, recipientId }: NotificationProps) => {
    try {
      const payload = {
        title,
        body,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(), // More reliable than new Date()
        status,
        userId,
        read: false,
        recipientId: recipientId || "",
      };

      await firebase.firestore().collection("Notifications").add(payload);
    } catch (error) {
      console.log("error while sending notification", error);
    }
  };

  useEffect(() => {
    if (!user?.userId) return;

    const unsubscribe = firebase
      .firestore()
      .collection("Notifications")
      .where("userId", "==", user?.userId)
      .where("read", "==", false)
      .onSnapshot(
        (snapshot) => {
          setUnreadCount(snapshot?.size || 0);
        },
        (error) => {
          console.error("Error fetching unread notifications in real-time:", error);
        },
      );

    return () => unsubscribe();
  }, [user?.userId]);

  const markNotificationsAsRead = async (userId: string | null) => {
    if (!userId) return;

    try {
      const snapshot = await firebase
        .firestore()
        .collection("Notifications")
        .where("userId", "==", userId)
        .where("read", "==", false)
        .get();

      const batch = firebase.firestore().batch();

      snapshot.forEach((doc) => {
        batch.update(doc.ref, { read: true });
      });

      await batch.commit();
      console.log("✅ All unread notifications marked as read.");
    } catch (error) {
      console.error("❌ Error marking notifications as read:", error);
    }
  };

  /**
   * Save the FCM token to Firestore if it’s different from the existing one
   */
  const saveTokenToFirestore = async (userId: string) => {
    if (!userId) return;

    try {
      const token = await messaging().getToken();

      if (!token) return;

      const userRef = firestore().collection("Users").doc(userId);
      const userDoc = await userRef.get();

      if (userDoc.exists) {
        const existingToken = userDoc.data()?.fcmToken;

        if (existingToken !== token) {
          await userRef.update({ fcmToken: token });
          console.log("FCM Token updated in Firestore.");
        } else {
          console.log("FCM Token is already up to date.");
        }
      }
    } catch (error) {
      console.error("Failed to get/store FCM token", error);
    }
  };

  /**
   * Checks and requests permission, then calls `saveTokenToFirestore`
   */
  const checkAndRequestPermission = async (userId: string) => {
    try {
      const authStatus = await messaging().hasPermission();
      const isEnabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (isEnabled) {
        await saveTokenToFirestore(userId);
      } else {
        const permission = await messaging().requestPermission();
        if (
          permission === messaging.AuthorizationStatus.AUTHORIZED ||
          permission === messaging.AuthorizationStatus.PROVISIONAL
        ) {
          await saveTokenToFirestore(userId);
        }
      }
    } catch (error) {
      console.error("Error checking/requesting permission", error);
    }
  };

  return {
    sendNotification,
    unreadCount,
    markNotificationsAsRead,
    checkAndRequestPermission,
  };
};
