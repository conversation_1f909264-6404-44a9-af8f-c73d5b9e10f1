import React from 'react';
import {View, StyleSheet, TouchableOpacity, Text} from 'react-native';
import Modal from 'react-native-modal';
import Video from 'react-native-video';

interface Props {
  isVisible: boolean;
  onClose: () => void;
}

const OnboardingVideoModal: React.FC<Props> = ({isVisible, onClose}) => {
  return (
    <Modal
      isVisible={isVisible}
      onSwipeComplete={onClose}
      backdropOpacity={0.6}
      swipeDirection="down"
      onBackdropPress={onClose}
      style={styles.modalContainer}>
      <View style={styles.modalContent}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>

        <Video
          source={{
            uri: 'https://firebasestorage.googleapis.com/v0/b/voitto-auth.appspot.com/o/Videos%2FvoittoExplainer.mp4?alt=media&token=6bf51370-5909-4a44-b8a9-40d41835198a',
          }}
          style={styles.video}
          controls
          resizeMode="contain"
          onEnd={onClose}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    margin: 0,
    justifyContent: 'center',
  },
  modalContent: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'black',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 10,
    borderRadius: 5,
    zIndex: 10,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default OnboardingVideoModal;
