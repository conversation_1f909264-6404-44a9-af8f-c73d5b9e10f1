import {
  StyleSheet,
  Text,
  TextInputProps,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import React, {ElementType} from 'react';
import {colors, fonts} from '../../utilities/theme';
interface Props extends TextInputProps {
  onPress?: () => void;
  title?: string;
  icon?: ElementType;
  selected?: boolean;
}

const OptionsItems: React.FC<Props> = ({
  onPress,
  title,
  icon: IconComponent,
  selected = false,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container, selected && styles.selectedContainer]}>
      {IconComponent && <IconComponent />}

      <Text style={styles.titleStyle}>{title}</Text>
    </TouchableOpacity>
  );
};

export default OptionsItems;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    marginHorizontal: 32,
    alignItems: 'center',
    paddingVertical: 15,
    borderRadius: 15,
    marginBottom: 20,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  selectedContainer: {
    borderColor: '#BB5427',
    borderWidth: 1, // Adjust thickness as needed
  },

  titleStyle: {
    fontSize: 16,
    fontFamily: fonts.Medium,
    color: '#555555',
    flex: 1,
    marginLeft: 22,
  },
});
