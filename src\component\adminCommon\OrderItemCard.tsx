import { View, Text, Image, StyleSheet, ViewStyle } from "react-native";
import React from "react";
import { colors, fonts } from "../../utilities/theme";
import moment from "moment";
import { FirebaseFirestoreTypes } from "@react-native-firebase/firestore";

interface Props {
  title: string;
  status: string;
  image: string;
  description: string;
  price: number;
  createdAt?: FirebaseFirestoreTypes.Timestamp;
  containerStyle?: ViewStyle;
}
const OrderItemCard: React.FC<Props> = ({ title, status, image, description, price, createdAt, containerStyle }) => {
  return (
    <View style={styles.productContainer}>
      <View style={[styles.rowContainer, containerStyle]}>
        <Image source={{ uri: image }} style={styles.image} />
        <View style={styles.productDetails}>
          <View style={styles.rowBetween}>
            <View style={[styles.chip, { backgroundColor: status === "PURCHASED" ? "#BB5427" : colors.black }]}>
              <Text style={styles.chipText}>{status}</Text>
            </View>
          </View>
          <Text numberOfLines={1} style={styles.description}>
            {description}
          </Text>
          <View style={styles.rowBetween}>
            <Text style={styles.price}>NS${price}</Text>
            <Text style={{ fontSize: 10, color: colors.gray[200], fontFamily: fonts.Regular }}>
              {createdAt?.seconds ? moment(createdAt.seconds * 1000).fromNow() : ""}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default OrderItemCard;

const styles = StyleSheet.create({
  productContainer: {},
  rowContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    paddingBottom: 8,
    borderColor: "#c4c3c0",
  },
  image: {
    width: 90,
    height: 80,
    borderRadius: 12,
  },
  productDetails: {
    flex: 1,
    justifyContent: "space-between",
    marginLeft: 10,
  },
  rowBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  chip: {
    backgroundColor: "#526CF9",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  chipText: {
    fontFamily: fonts.Regular,
    fontSize: 10,
    color: colors.white,
  },
  description: {
    fontSize: 10,
    fontFamily: fonts.Regular,
    color: "#555555",
  },
  price: {
    fontSize: 20,
    color: colors.black,
    fontFamily: fonts.SemiBold,
  },
});
