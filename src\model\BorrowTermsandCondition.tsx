import { Platform, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import Modal from "react-native-modal";
import { colors, fonts } from "../utilities/theme";

interface Props {
  isModalVisible: boolean;
  setModalVisible: (val: boolean) => void;
  onCanclePress: () => void;
  onPressBorrow: () => void;
  borrowingPrice: number;
  borrowingSecurity: number;
}

const BorrowTermsandCondition: React.FC<Props> = ({
  isModalVisible,
  setModalVisible,
  onCanclePress,
  onPressBorrow,
  borrowingPrice,
  borrowingSecurity,
}) => {
  const TextItem = ({ text, percent }: { text: string; percent?: string }) => {
    return (
      <View style={styles.textItemContainer}>
        <View style={styles.bulletPoint} />
        <Text style={styles.textItemText}>
          <Text style={styles.textItemBold}>{percent ? percent : " "}</Text> {text}
        </Text>
      </View>
    );
  };

  return (
    <Modal isVisible={isModalVisible} onBackdropPress={() => setModalVisible(false)}>
      <View style={styles.modalContainer}>
        <Text style={styles.modalTitle}>Terms & Conditions</Text>
        {borrowingPrice ? (
          <TextItem percent={`NS$${borrowingPrice}`} text="Borrowing fee for accessing the product" />
        ) : null}
        {borrowingSecurity ? (
          <TextItem
            percent={`NS$${borrowingSecurity}`}
            text="Refundable security deposit held during the borrowing period"
          />
        ) : null}
        <TextItem
          percent="100%"
          text="Full deposit is refunded if the stick is returned in the same condition it was sent."
        />
        <TextItem
          percent="0%"
          text="Deposit will be retained by VOITTO if the stick is badly damaged, broken, or unfit for future borrowing."
        />

        <TextItem text="Borrowing time frame applies" />
        <TextItem text="Free return shipping and packaging included" />
        <TextItem text="This transaction is built on mutual trust" />

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onCanclePress}>
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={onPressBorrow}>
            <Text style={styles.buttonText}>Borrow</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default BorrowTermsandCondition;

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    paddingRight: 20,
  },
  modalTitle: {
    fontSize: 18,
    color: colors.primary,
    fontFamily: fonts.SemiBold,
  },
  textItemContainer: {
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 6,
  },
  bulletPoint: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#BB5427",
    marginTop: Platform.OS === "android" ? 4 : 3,
  },
  textItemText: {
    fontSize: 12,
    color: "#555555",
    fontFamily: fonts.Regular,
    flex: 1,
  },
  textItemBold: {
    fontFamily: fonts.SemiBold,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 15,
    marginTop: 100,
  },
  button: {
    flex: 1,
    borderRadius: 12,
    backgroundColor: colors.primary,
    height: 42,
    alignItems: "center",
    justifyContent: "center",
  },
  cancelButton: {
    backgroundColor: "#023e3f",
  },
  buttonText: {
    color: "white",
    textAlign: "center",
    fontFamily: fonts.SemiBold,
  },
});
