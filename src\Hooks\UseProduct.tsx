import { useState } from "react";
import firestore from "@react-native-firebase/firestore";
import { IProducts } from "../interfaces/Products.ts";
import { useUser } from "./UseContext.tsx";
import { showToast } from "../helper/toast.ts";
import { ICart, ICartItem } from "../interfaces/ICart.ts";

const UseProduct = () => {
  const { user } = useUser();

  const onPressWishList = async (item: IProducts) => {
    if (!user) return null;

    const isFavorited = item.wishList && item.wishList.includes(user.userId);

    const updatedWishList =
      isFavorited && item.wishList
        ? item.wishList.filter((userId) => userId !== user.userId)
        : [...(item.wishList || []), user.userId];

    try {
      await firestore()
        .collection("Products")
        .doc(item.id)
        .update({
          wishList: isFavorited
            ? firestore.FieldValue.arrayRemove(user.userId)
            : firestore.FieldValue.arrayUnion(user.userId),
        });

      // console.log(isFavorited ? 'Removed from wishlist' : 'Added to wishlist');
      return updatedWishList;
    } catch (error) {
      console.error(isFavorited ? "Error removing from wishlist:" : "Error adding to wishlist:", error);
      return item.wishList;
    }
  };

  const onPressMyCart = async (item: IProducts, productStatus: "PURCHASED" | "BORROWED") => {
    if (!user) return;
    try {
      const userRef = firestore().collection("Users").doc(user.userId);
      const userDoc = await userRef.get();
      const myCart: ICart["myCart"] = userDoc.data()?.myCart || [];

      if (myCart.find((cartItem) => cartItem.productId === item.id)) {
        showToast("You already have this product in your cart.");
        return null;
      }
      if (myCart.length >= 2) {
        showToast("You can only add two products.");
        return null;
      }
      const newProduct: ICartItem = {
        productId: item.id,
        orderType: productStatus,
        createdAt: firestore.Timestamp.now(),
        quantity: 1,
      };

      await userRef.set({ myCart: [...myCart, newProduct] }, { merge: true });
      return [...myCart, newProduct];
    } catch (error) {
      console.error("Error adding product to myCart:", error);
      return null;
    }
  };

  const fetchOrderIds = (userId: string): Promise<[string[], () => void]> => {
    return new Promise((resolve, reject) => {
      if (!userId) {
        console.log("User ID is not defined.");
        resolve([[], () => {}]);
        return;
      }

      // Set up a real-time listener on the user's document in the `Users` collection
      const unsubscribe = firestore()
        .collection("Users")
        .doc(userId)
        .onSnapshot(
          (doc) => {
            if (doc.exists) {
              const userData = doc.data();
              const orderIds = userData?.myOrders || [];
              console.log("Fetched Order IDs:", orderIds);
              resolve([orderIds, unsubscribe]);
            } else {
              console.log("User document does not exist.");
              resolve([[], unsubscribe]);
            }
          },
          (error) => {
            console.error("Error fetching order IDs:", error);
            reject(error);
          },
        );
    });
  };

  return {
    onPressWishList,
    onPressMyCart,
    fetchOrderIds,
  };
};

export default UseProduct;
