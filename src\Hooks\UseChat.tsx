import { useState } from "react";
import { IChat } from "../interfaces/IChat";
import { useUser } from "./UseContext";
import firestore from "@react-native-firebase/firestore";
import { IMessage, GiftedChat } from "react-native-gifted-chat";
import useNotification from "./useNotification";

export default () => {
  const { sendNotification } = useNotification();
  const { user } = useUser();
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversations, setConversations] = useState<IChat[]>([]);
  const [messageLoading, setMessageLoading] = useState(true);

  const resetMessages = () => {
    setMessages([]);
  };
  const onSend = async (messages: IMessage[], recipientId: string) => {
    if (!user) return;
    const msg = messages[0];

    const myMessage = {
      ...msg,
      sentBy: user.userId,
      sentTo: recipientId,
      createdAt: new Date(),
      read: false,
      currentMembers: [user.userId, recipientId],
    };

    setMessages((previousMessages) => GiftedChat.append(previousMessages, [myMessage]));
    const uid =
      user.userId && recipientId > user.userId ? user.userId + "-" + recipientId : recipientId + "-" + user.userId;

    const convPayload = {
      members: [user.userId, recipientId],
      lastMessage: myMessage,
      currentMembers: [user.userId, recipientId],
    };

    await firestore()
      .collection("Conversations")
      .doc(uid)
      .set(convPayload)
      .then(() => {
        firestore().collection("Conversations").doc(uid).collection("messages").add(myMessage);
      });
    sendNotification({
      title: user?.name || "User",
      body: msg.text,
      status: "Refund Product",
      userId: recipientId,
      recipientId: user?.userId || "",
    });
  };

  const getAllMessages = (recipientId: string) => {
    if (!recipientId || !user?.userId) return;

    const uid = recipientId > user.userId ? `${user.userId}-${recipientId}` : `${recipientId}-${user.userId}`;

    const query = firestore()
      .collection("Conversations")
      .doc(uid)
      .collection("messages")
      .where("currentMembers", "array-contains", user.userId)
      .orderBy("createdAt", "desc");

    // Attach listener
    const unsubscribe = query.onSnapshot(
      (snapShot) => {
        if (!snapShot.empty) {
          const allMsg = snapShot.docs.map((docSnap) => ({
            ...docSnap.data(),
            createdAt: docSnap.data().createdAt.toDate(),
          })) as IMessage[];

          setMessages(allMsg);
        }
        setTimeout(() => {
          setMessageLoading(false);
        }, 500);
      },
      (error) => {
        console.log("Error fetching messages:", error);
        setTimeout(() => {
          setMessageLoading(false);
        }, 500);
      },
    );

    return unsubscribe; // Return unsubscribe function
  };

  const getConversations = async () => {
    setIsLoading(true);
    try {
      const query = firestore().collection("Conversations").where("currentMembers", "array-contains", user?.userId);

      query.onSnapshot(
        (snapShot) => {
          const chats: IChat[] = snapShot.docs.reduce((accumulator: IChat[], chat) => {
            accumulator.push({ ...chat.data(), key: chat.id } as IChat);
            return accumulator;
          }, []);
          setConversations(chats);
        },
        (error) => {
          console.error("Error in getConversations:", error);
        },
      );
    } catch (error) {
      console.error("Error in getConversations:", error);
      // Handle the error appropriately (e.g., show an error message to the user)
    } finally {
      setIsLoading(false);
    }
  };
  return {
    onSend,
    getAllMessages,
    isLoading,
    messages,
    getConversations,
    conversations,
    resetMessages,
    messageLoading,
  };
};
