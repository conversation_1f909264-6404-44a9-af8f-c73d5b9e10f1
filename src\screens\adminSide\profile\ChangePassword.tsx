import {
  Text,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {IUser} from '../../../interfaces/IUser';
import {AdminBottomTabParamlist} from '../../../navigation/AdminBottomTabs';
import {AdminStackParamsList} from '../../../navigation/AdminNavigation';
import {AppButton, AuthHeader, ProfileInput} from '../../../component';
import {colors} from '../../../utilities/theme';
import auth, {firebase} from '@react-native-firebase/auth';
import {showToast} from '../../../helper/toast';
import CustomHeader from '../../../component/common/CustomHeader';

type Props = NativeStackScreenProps<
  AdminBottomTabParamlist & AdminStackParamsList,
  'ChangePassword'
>;

const ChangePassword: React.FC<Props> = ({navigation}) => {
  const [loading, setLoading] = useState(false);
  const validationSchema = Yup.object().shape({
    oldPassword: Yup.string()
      .required('Old password is required')
      .max(30, 'Max 30 characters'),
    newPassword: Yup.string()
      .required('New password is required')
      .min(6, 'Password must be at least 6 characters')
      .max(30, 'Max 30 characters'),
    confirmNewPassword: Yup.string()
      .required('Confirm new password is required')
      .oneOf([Yup.ref('newPassword')], 'Passwords must match'),
  });

  const formik = useFormik({
    initialValues: {
      oldPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },

    validationSchema: validationSchema,
    onSubmit: async values => {
      const {oldPassword, newPassword} = values;
      await handleSaveChanges(oldPassword, newPassword);
    },
  });
  const handleSaveChanges = async (oldPassword: any, newPassword: any) => {
    const user = auth().currentUser;
    setLoading(true);

    if (user && user.email) {
      try {
        await auth().signInWithEmailAndPassword(user.email, oldPassword);

        await user.updatePassword(newPassword);

        showToast('Password updated successfully', 'success', 'success');
        setLoading(false);
        navigation.goBack();
      } catch (error) {
        const firebaseError = error as any;
        if (
          firebaseError.code === 'auth/wrong-password' ||
          firebaseError.code === 'auth/invalid-credential'
        ) {
          showToast('Old password is incorrect', 'error', 'error');
          setLoading(false);
        } else if (firebaseError.code === 'auth/weak-password') {
          showToast('New password is too weak', 'error', 'error');
          setLoading(false);
        } else {
          showToast('Something went wrong', 'error', 'error');
          setLoading(false);
        }
      }
    } else {
      console.log('Error', 'User not logged in or email not found');
      setLoading(false);
    }
  };
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{marginTop: 20}}
        showsVerticalScrollIndicator={false}>
        <ProfileInput
          label="Old Password"
          placeholder="Old Password"
          value={formik.values.oldPassword}
          onChangeText={formik.handleChange('oldPassword')}
          onBlur={formik.handleBlur('oldPassword')}
          errorMessage={formik.touched.oldPassword && formik.errors.oldPassword}
        />
        <ProfileInput
          label="New Password"
          placeholder="New Password"
          value={formik.values.newPassword}
          onChangeText={formik.handleChange('newPassword')}
          onBlur={formik.handleBlur('newPassword')}
          errorMessage={formik.touched.newPassword && formik.errors.newPassword}
        />
        <ProfileInput
          label="Confirm New Password"
          placeholder="Confirm New Password"
          value={formik.values.confirmNewPassword}
          onChangeText={formik.handleChange('confirmNewPassword')}
          onBlur={formik.handleBlur('confirmNewPassword')}
          errorMessage={
            formik.touched.confirmNewPassword &&
            formik.errors.confirmNewPassword
          }
        />
        <AppButton
          title="Save Changes"
          onPress={formik.handleSubmit}
          customStyle={{marginTop: 16}}
          isLoading={loading}
        />
      </ScrollView>
    </View>
  );
};

export default ChangePassword;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 28,
    backgroundColor: colors.white,
  },
});
