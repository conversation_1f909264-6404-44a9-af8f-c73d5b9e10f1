import { StyleSheet, View, Text, TextInputProps, useWindowDimensions } from "react-native";
import React, { useState } from "react";
import { appStyles, colors, fonts } from "../../utilities/theme";
import RenderHtml from "react-native-render-html";

interface Props extends TextInputProps {
  productDescription?: string;
}

const DescriptionItem: React.FC<Props> = ({ productDescription }) => {
  const source = {
    html: productDescription || "",
  };

  const htmlStyles = {
    color: "#000000",
  };
  const { width } = useWindowDimensions();
  console.log("---------------", productDescription);

  return (
    <View style={styles.container}>
      <Text style={appStyles.h3}>Description</Text>
      <Text style={{ color: "black" }}>{productDescription}</Text>
      {/* <RenderHtml
        contentWidth={width}
        source={source}
        tagsStyles={{
          p: htmlStyles,
          div: htmlStyles,
          strong: htmlStyles,
          li: htmlStyles,
        }}
      /> */}
    </View>
  );
};

export default DescriptionItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 18,
    marginHorizontal: 27,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 2,
  },
  text1: {
    fontSize: 12,
    fontFamily: fonts.Regular,
    lineHeight: 18,
    color: colors.gray[50],
  },
});
