import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TextInput,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {images} from '../../assets/images';
import {FC, ReactNode, useState} from 'react';
import {colors, fonts} from '../../utilities/theme';

interface Props extends TextInputProps {
  containerStyle?: ViewStyle;
  icon?: ReactNode;
  isPassword?: boolean;
  onLeftIconPress?: () => void;
  onRightIconPress?: () => void;
  rightIcon?: ImageSourcePropType;
  inputStyles?: TextStyle;
  errorMessage?: string | false;
  inputContainerStyle?: ViewStyle;
}

const FormInput: FC<Props> = ({
  onChangeText,
  value,
  placeholder,
  containerStyle,
  icon,
  isPassword,
  secureTextEntry,
  onLeftIconPress,
  onRightIconPress,
  rightIcon,
  inputStyles,
  onBlur,
  keyboardType,
  editable,
  errorMessage,
  multiline = false,
  inputContainerStyle,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={containerStyle}>
      <View
        style={[
          styles.inputContainer,
          {
            backgroundColor: value ? '#fcf6f4' : 'white',
            marginTop: 25,
            ...(!value ? styles.inputContainerShadow : {}),
          },
          inputContainerStyle,
        ]}>
        {icon}
        <TextInput
          style={[
            styles.textInput,
            inputStyles,
            {color: value ? colors.primary : colors.primary},
            {textAlignVertical: multiline ? 'top' : 'auto'},
          ]}
          placeholder={placeholder}
          placeholderTextColor={`${colors.black}50`}
          onChangeText={onChangeText}
          value={value}
          secureTextEntry={secureTextEntry}
          onBlur={e => {
            onBlur?.(e);
            setIsFocused(false);
          }}
          onFocus={() => setIsFocused(true)}
          keyboardType={keyboardType}
          editable={editable}
          multiline={multiline}
          numberOfLines={multiline ? 10 : 1}
          {...rest}
        />
        {isPassword ? (
          <TouchableOpacity onPress={onLeftIconPress}>
            <Image
              source={secureTextEntry ? images.hideicon : images.unhideicon}
              style={[
                styles.leftIconContainer,
                {tintColor: value ? colors.primary : `${colors.black}60`},
              ]}
            />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={onRightIconPress}>
            {rightIcon ? (
              <Image
                source={rightIcon}
                style={[
                  styles.leftIconContainer,
                  {tintColor: value ? '#FF0000' : '#9E9E9E'},
                ]}
              />
            ) : null}
          </TouchableOpacity>
        )}
      </View>

      {errorMessage && <Text style={styles.errorText}>{errorMessage}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    height: 57,
    borderRadius: 15,
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    alignItems: 'center',
    flexDirection: 'row',
  },
  inputContainerShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  leftIconContainer: {
    width: 20,
    height: 20,
  },
  textInput: {
    fontSize: 14,
    fontFamily: fonts.Medium,
    color: colors.black,
    flex: 1,
  },
  errorText: {
    color: colors.red2,
    marginTop: 5,
    fontSize: 12,
    fontFamily: fonts.Medium,
  },
});

export default FormInput;
